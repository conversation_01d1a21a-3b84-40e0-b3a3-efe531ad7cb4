using AcademicPerformance.Consts;
using Microsoft.AspNetCore.Authorization;
using Rlx.Shared.Helpers;
namespace AcademicPerformance.Configurations
{
    /// <summary>
    /// AcademicPerformance projesi için claim-based authorization policy konfigürasyonu
    /// Rlx.Shared PolicyHelper pattern'ini kullanarak granüler yetkilendirme sağlar
    /// </summary>
    public static class PolicyConfig
    {
        /// <summary>
        /// AcademicPerformance projesi için tüm authorization policy'lerini yapılandırır
        /// Policy format: "permission.action.ap.{actionName}" veya "permission.page.ap.{pageName}"
        /// </summary>
        public static void ConfigurePolicies(this AuthorizationOptions options)
        {
            // Page access policies
            options.AddPage("ap");
            // Core action policies - AcademicPerformance specific
            options.AddAction("managecriteria");      // Kriter şablonları yönetimi
            options.AddAction("submitdata");          // Performans verisi gönderme
            options.AddAction("approvesubmissions");  // Gönderileri onaylama/reddetme
            options.AddAction("manageforms");         // Değerlendirme formları yönetimi
            options.AddAction("viewreports");         // Raporları görüntüleme
            options.AddAction("inputdepartmentdata"); // Bölüm performans verisi girme
            options.AddAction("evaluatestaff");       // Personel yetkinlik değerlendirme
            options.AddAction("verifyportfolio");     // Portfolio öğelerini doğrulama

            // General access policies
            options.AddAction("allaccess");           // Genel erişim (authenticated kullanıcılar)
            options.AddAction("viewstaticdata");      // Statik kriter verilerini görüntüleme
            options.AddAction("accessreporting");     // Raporlama dashboard'ına erişim
            options.AddAction("requireadminrole");    // Admin rolü gerektiren işlemler

            // File operations policies
            options.AddAction("uploadfiles");         // Dosya yükleme
            options.AddAction("downloadfiles");       // Dosya indirme
            options.AddAction("deletefiles");         // Dosya silme
            options.AddAction("managefiles");         // Dosya yönetimi (tüm file operations)

            // Submission management policies
            options.AddAction("reviewsubmissions");           // Submission'ları review etme
            options.AddAction("managesubmissionworkflow");    // Submission workflow yönetimi
            options.AddAction("accesssubmissiondetails");     // Submission detaylarına erişim
            options.AddAction("downloadevidencefiles");       // Evidence file'ları indirme
            options.AddAction("assignsubmissions");           // Submission'ları controller'lara atama
            options.AddAction("viewsubmissions");             // Submission'ları görüntüleme

            // Controller management policies
            options.AddAction("viewcontrollerdashboard");     // Controller dashboard'ına erişim
            options.AddAction("managecontrollersettings");    // Controller ayarları yönetimi

            // Data management policies
            options.AddAction("viewdata");                    // Veri görüntüleme
            options.AddAction("editdata");                    // Veri düzenleme

            // Audit and monitoring policies
            options.AddAction("viewaudittrail");              // Audit trail görüntüleme

            // Test action (development/testing purposes)
            options.AddAction("test");
        }
        /// <summary>
        /// Page access policy oluşturur
        /// Policy name format: "permission.page.ap.{pageName}"
        /// Admin bypass: Çeşitli admin claim'leri ile bypass edilebilir
        /// </summary>
        private static void AddPage(this AuthorizationOptions options, string val)
        {
            options.AddRlxPolicy([
                new Rlx.Shared.Models.RlxPolicy { ClaimType = APConsts.PermissionPageAP, ClaimValue = val },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = APConsts.PermissionPageAll, ClaimValue = "all" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = APConsts.PermissionPageAP, ClaimValue = "all" },
                // Admin bypass options - çeşitli admin claim'leri
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "role", ClaimValue = "SuperAdmin" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "role", ClaimValue = "Admin" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.action", ClaimValue = "all" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.page", ClaimValue = "all" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.page", ClaimValue = "ap" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.action", ClaimValue = "ap" }
            ]);
        }
        /// <summary>
        /// Action-based policy oluşturur
        /// Policy name format: "permission.action.ap.{actionName}"
        /// Kullanıcı şu claim'lerden birine sahip olmalı:
        /// - permission.action.ap = {actionName}
        /// - permission.action.all = "all"
        /// - permission.action.ap = "all"
        /// - Çeşitli admin claim'leri (Admin bypass)
        /// </summary>
        private static void AddAction(this AuthorizationOptions options, string val)
        {
            options.AddRlxPolicy([
                new Rlx.Shared.Models.RlxPolicy { ClaimType = APConsts.PermissionActionAP, ClaimValue = val },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = APConsts.PermissionActionAll, ClaimValue = "all" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = APConsts.PermissionActionAP, ClaimValue = "all" },
                // Admin bypass options - çeşitli admin claim'leri
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "role", ClaimValue = "SuperAdmin" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "role", ClaimValue = "Admin" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.action", ClaimValue = "all" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.page", ClaimValue = "all" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.page", ClaimValue = "ap" },
                new Rlx.Shared.Models.RlxPolicy { ClaimType = "permission.action", ClaimValue = "ap" }
            ]);
        }
    }
}
