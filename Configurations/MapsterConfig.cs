using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using Mapster;

namespace AcademicPerformance.Configurations
{
    public static class MapsterConfig
    {
        public static void RegisterMappings()
        {

        }

        public static void Configure()
        {
            // UserContextCo to UserProfileDto mapping
            TypeAdapterConfig<UserContextCo, UserProfileDto>.NewConfig().TwoWays();

            // Submission Document to DTO mappings
            TypeAdapterConfig<AcademicSubmissionDocument, SubmissionDto>.NewConfig();
            TypeAdapterConfig<SubmissionDto, AcademicSubmissionDocument>.NewConfig();

            // Criterion Data mappings
            TypeAdapterConfig<SubmissionCriterionData, SubmissionCriterionDataDto>.NewConfig();
            TypeAdapterConfig<SubmissionCriterionDataDto, SubmissionCriterionData>.NewConfig();

            // Data Entry mappings
            TypeAdapterConfig<CriterionDataEntry, CriterionDataEntryDto>.NewConfig();
            TypeAdapterConfig<CriterionDataEntryDto, CriterionDataEntry>.NewConfig();

            // Feedback Entity to DTO mappings
            TypeAdapterConfig<FeedbackEntryEntity, FeedbackEntryDto>.NewConfig()
                .Map(dest => dest.ControllerUserId, src => src.CreatedBy)
                .Map(dest => dest.Message, src => src.Comments)
                .Map(dest => dest.CreatedByUserId, src => src.CreatedBy);

            TypeAdapterConfig<SubmissionFeedbackEntity, FeedbackEntryDto>.NewConfig()
                .Map(dest => dest.Comments, src => src.Message)
                .Map(dest => dest.CreatedByUserId, src => src.CreatedByUserId);

            TypeAdapterConfig<CriterionFeedbackEntity, CriterionFeedbackDto>.NewConfig()
                .Map(dest => dest.CriterionId, src => src.CriterionLinkId)
                .Map(dest => dest.CriterionName, src => src.CriterionLink != null ? src.CriterionLink.CriterionType : "")
                .Map(dest => dest.FeedbackMessage, src => src.FeedbackMessage);

            // DTO to Entity mappings (Create/Update operasyonları için)
            TypeAdapterConfig<RevisionRequestDto, SubmissionFeedbackEntity>.NewConfig()
                .Map(dest => dest.Message, src => src.GeneralMessage)
                .Map(dest => dest.FeedbackType, src => "RevisionRequest")
                .Map(dest => dest.CreatedAt, src => DateTime.UtcNow)
                .Ignore(dest => dest.Id)
                .Ignore(dest => dest.ControllerUserId)
                .Ignore(dest => dest.SubmissionStatusAtTime);

            TypeAdapterConfig<ApprovalFeedbackDto, SubmissionFeedbackEntity>.NewConfig()
                .Map(dest => dest.Message, src => src.Comments ?? "Submission approved")
                .Map(dest => dest.FeedbackType, src => "Approval")
                .Map(dest => dest.CreatedAt, src => DateTime.UtcNow)
                .Ignore(dest => dest.Id)
                .Ignore(dest => dest.ControllerUserId)
                .Ignore(dest => dest.SubmissionStatusAtTime);

            TypeAdapterConfig<CriterionRevisionRequestDto, CriterionFeedbackEntity>.NewConfig()
                .Map(dest => dest.CriterionLinkId, src => src.CriterionId)
                .Map(dest => dest.FeedbackMessage, src => src.RevisionMessage)
                .Map(dest => dest.Status, src => "NeedsRevision")
                .Map(dest => dest.CreatedAt, src => DateTime.UtcNow)
                .Ignore(dest => dest.Id)
                .Ignore(dest => dest.SubmissionFeedbackId)
                .Ignore(dest => dest.SubmissionId);

            TypeAdapterConfig<CriterionApprovalDto, CriterionFeedbackEntity>.NewConfig()
                .Map(dest => dest.CriterionLinkId, src => src.CriterionId)
                .Map(dest => dest.FeedbackMessage, src => src.Comment ?? "Approved")
                .Map(dest => dest.Status, src => src.Status)
                .Map(dest => dest.CreatedAt, src => DateTime.UtcNow)
                .Ignore(dest => dest.Id)
                .Ignore(dest => dest.SubmissionFeedbackId)
                .Ignore(dest => dest.SubmissionId);

        }
    }
}
