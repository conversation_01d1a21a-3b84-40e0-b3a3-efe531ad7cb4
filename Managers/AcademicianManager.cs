using Mapster;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
using System.Text.Json;
namespace AcademicPerformance.Managers
{
    public class AcademicianManager : IAcademicianManager
    {
        private readonly IAcademicianStore _academicianStore;
        private readonly IFormStore _formStore;
        private readonly IUserDataService _userDataService;
        private readonly IOrganizationManagementApiService _organizationApiService;
        private readonly ILogger<AcademicianManager> _logger;
        public AcademicianManager(
            IAcademicianStore academicianStore,
            IFormStore formStore,
            IUserDataService userDataService,
            IOrganizationManagementApiService organizationApiService,
            ILogger<AcademicianManager> logger)
        {
            _academicianStore = academicianStore;
            _formStore = formStore;
            _userDataService = userDataService;
            _organizationApiService = organizationApiService;
            _logger = logger;
        }
        #region Dashboard Operations
        public async Task<AcademicianDashboardDto> GetAcademicianDashboardAsync(string universityUserId)
        {
            // Akademisyen profilini getir veya sync et
            var profile = await GetOrSyncAcademicianProfileAsync(universityUserId);
            if (profile == null)
            {
                throw new InvalidOperationException($"Academician profile not found for user {universityUserId}");
            }
            // Atanmış formları getir
            var assignedForms = await GetAssignedFormsAsync(universityUserId);
            // Dashboard istatistiklerini getir
            var statistics = await GetDashboardStatisticsAsync(universityUserId);
            // Profil sync gerekli mi kontrol et (24 saatten eski ise)
            var isProfileSyncRequired = profile.LastSyncedAt < DateTime.UtcNow.AddHours(-24);
            return new AcademicianDashboardDto
            {
                AcademicianUserId = universityUserId,
                AcademicianName = profile.Name,
                AcademicianFullName = profile.FullName,
                AcademicCadre = profile.AcademicCadre ?? "Unknown",
                Department = profile.Department ?? "Unknown",
                Email = profile.Email,
                Title = profile.Title,
                AssignedForms = assignedForms,
                Statistics = statistics,
                LastUpdated = DateTime.UtcNow,
                LastProfileSyncedAt = profile.LastSyncedAt,
                IsProfileSyncRequired = isProfileSyncRequired
            };
        }
        public async Task<List<AssignedFormDto>> GetAssignedFormsAsync(string universityUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            if (profile?.AcademicCadre == null)
            {
                return new List<AssignedFormDto>();
            }
            // Bu akademik kadro için aktif formları getir
            var activeForms = await GetActiveFormsForAcademicCadreAsync(profile.AcademicCadre);
            // Her form için submission durumunu getir
            foreach (var form in activeForms)
            {
                form.SubmissionStatus = await GetSubmissionStatusAsync(universityUserId, form.Id!);
                // Deadline bilgilerini hesapla
                if (form.SubmissionDeadline.HasValue)
                {
                    var daysUntilDeadline = (form.SubmissionDeadline.Value - DateTime.UtcNow).Days;
                    form.DaysUntilDeadline = daysUntilDeadline;
                    form.IsDeadlineApproaching = daysUntilDeadline <= 7 && daysUntilDeadline >= 0;
                }
                // İzinleri ayarla
                form.CanEdit = await CanAcademicianEditSubmissionAsync(universityUserId, form.Id!);
                form.CanSubmit = await CanAcademicianSubmitFormAsync(universityUserId, form.Id!);
            }
            return activeForms;
        }
        public async Task<DashboardStatisticsDto> GetDashboardStatisticsAsync(string universityUserId)
        {
            var assignedForms = await GetAssignedFormsAsync(universityUserId);
            var submissionStatuses = await GetAllSubmissionStatusesAsync(universityUserId);
            var statistics = new DashboardStatisticsDto
            {
                TotalAssignedForms = assignedForms.Count,
                NotStartedForms = assignedForms.Count(f => f.SubmissionStatus?.Status == "NotStarted" || f.SubmissionStatus == null),
                DraftForms = submissionStatuses.Count(s => s.Status == "Draft"),
                SubmittedForms = submissionStatuses.Count(s => s.Status == "Submitted"),
                ApprovedForms = submissionStatuses.Count(s => s.Status == "Approved"),
                RejectedForms = submissionStatuses.Count(s => s.Status == "Rejected"),
                FormsRequiringRevision = submissionStatuses.Count(s => s.Status == "RequiresRevision"),
                FormsWithApproachingDeadlines = assignedForms.Count(f => f.IsDeadlineApproaching)
            };
            // Genel tamamlanma yüzdesini hesapla
            if (statistics.TotalAssignedForms > 0)
            {
                var completedForms = statistics.ApprovedForms;
                statistics.OverallCompletionPercentage = (double)completedForms / statistics.TotalAssignedForms * 100;
            }
            return statistics;
        }
        #endregion
        #region Profile Management
        public async Task<AcademicianProfileDto?> GetAcademicianProfileAsync(string universityUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            return profile?.Adapt<AcademicianProfileDto>();
        }
        public async Task<List<AcademicianProfileDto>> GetAcademicianProfilesAsync()
        {
            var profiles = await _academicianStore.GetActiveAcademicianProfilesAsync();
            return profiles.Adapt<List<AcademicianProfileDto>>();
        }
        public async Task<List<AcademicianProfileDto>> GetAcademicianProfilesByDepartmentAsync(string department)
        {
            var profiles = await _academicianStore.GetAcademicianProfilesByDepartmentAsync(department);
            return profiles.Adapt<List<AcademicianProfileDto>>();
        }
        public async Task<List<AcademicianProfileDto>> GetAcademicianProfilesByAcademicCadreAsync(string academicCadre)
        {
            var profiles = await _academicianStore.GetAcademicianProfilesByAcademicCadreAsync(academicCadre);
            return profiles.Adapt<List<AcademicianProfileDto>>();
        }
        public async Task<List<AcademicianProfileDto>> SearchAcademicianProfilesAsync(string searchTerm)
        {
            var profiles = await _academicianStore.SearchAcademicianProfilesAsync(searchTerm);
            return profiles.Adapt<List<AcademicianProfileDto>>();
        }
        #endregion
        #region Profile Sync Operations
        public async Task<AcademicianProfileSyncResultDto> SyncAcademicianProfileAsync(string universityUserId, bool forceSync = false)
        {
            var syncDto = new AcademicianProfileSyncDto
            {
                UniversityUserId = universityUserId,
                Name = "", // Will be filled from API
                Surname = "", // Will be filled from API
                ForceSync = forceSync
            };
            return await SyncAcademicianProfileAsync(syncDto);
        }
        public async Task<AcademicianProfileSyncResultDto> SyncAcademicianProfileAsync(AcademicianProfileSyncDto syncDto)
        {
            var result = new AcademicianProfileSyncResultDto
            {
                UniversityUserId = syncDto.UniversityUserId,
                Action = "NoChange", // Default value
                SyncedAt = DateTime.UtcNow
            };
            try
            {
                // Check if profile exists
                var existingProfile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(syncDto.UniversityUserId);
                // Check if sync is needed
                if (!syncDto.ForceSync && existingProfile != null &&
                    existingProfile.LastSyncedAt > DateTime.UtcNow.AddHours(-1))
                {
                    result.Success = true;
                    result.Action = "NoChange";
                    result.Profile = existingProfile.Adapt<AcademicianProfileDto>();
                    return result;
                }
                // OrganizationManagement API'den veri al
                var userProfile = await _userDataService.GetUserProfileAsync(syncDto.UniversityUserId);
                var department = await _userDataService.GetUserDepartmentAsync(syncDto.UniversityUserId);
                var academicCadre = await _userDataService.GetUserAcademicCadreAsync(syncDto.UniversityUserId);
                if (existingProfile == null)
                {
                    // FullName'i parse et, ad ve soyad çıkar
                    var nameParts = userProfile.FullName?.Split(' ', StringSplitOptions.RemoveEmptyEntries) ?? new[] { "Unknown" };
                    var firstName = nameParts.FirstOrDefault() ?? "Unknown";
                    var lastName = nameParts.Length > 1 ? string.Join(" ", nameParts.Skip(1)) : "";
                    // Yeni profil oluştur
                    var newProfile = new AcademicianProfileEntity
                    {
                        Id = Guid.NewGuid().ToString(),
                        UniversityUserId = syncDto.UniversityUserId,
                        Name = !string.IsNullOrEmpty(syncDto.Name) ? syncDto.Name : firstName,
                        Surname = !string.IsNullOrEmpty(syncDto.Surname) ? syncDto.Surname : lastName,
                        Department = syncDto.Department ?? department,
                        AcademicCadre = syncDto.AcademicCadre ?? academicCadre,
                        Email = syncDto.Email ?? userProfile.Email,
                        Title = syncDto.Title,
                        SyncNotes = "Created via sync operation"
                    };
                    var createdProfile = await _academicianStore.CreateAcademicianProfileAsync(newProfile);
                    result.Success = true;
                    result.Action = "Created";
                    result.Profile = createdProfile.Adapt<AcademicianProfileDto>();
                }
                else
                {
                    // FullName'i parse et, ad ve soyad çıkar
                    var nameParts = userProfile.FullName?.Split(' ', StringSplitOptions.RemoveEmptyEntries) ?? new[] { "Unknown" };
                    var firstName = nameParts.FirstOrDefault() ?? existingProfile.Name;
                    var lastName = nameParts.Length > 1 ? string.Join(" ", nameParts.Skip(1)) : existingProfile.Surname;
                    // Mevcut profili güncelle
                    existingProfile.Name = !string.IsNullOrEmpty(syncDto.Name) ? syncDto.Name : firstName;
                    existingProfile.Surname = !string.IsNullOrEmpty(syncDto.Surname) ? syncDto.Surname : lastName;
                    existingProfile.Department = syncDto.Department ?? department ?? existingProfile.Department;
                    existingProfile.AcademicCadre = syncDto.AcademicCadre ?? academicCadre ?? existingProfile.AcademicCadre;
                    existingProfile.Email = syncDto.Email ?? userProfile.Email ?? existingProfile.Email;
                    existingProfile.Title = syncDto.Title ?? existingProfile.Title;
                    existingProfile.LastSyncedAt = DateTime.UtcNow;
                    existingProfile.SyncNotes = "Updated via sync operation";
                    await _academicianStore.UpdateAcademicianProfileAsync(existingProfile);
                    result.Success = true;
                    result.Action = "Updated";
                    result.Profile = existingProfile.Adapt<AcademicianProfileDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing academician profile for user {UserId}", syncDto.UniversityUserId);
                result.Success = false;
                result.Action = "Error";
                result.ErrorMessage = ex.Message;
            }
            return result;
        }
        #endregion
        #region Helper Methods
        private async Task<AcademicianProfileEntity?> GetOrSyncAcademicianProfileAsync(string universityUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            if (profile == null)
            {
                // Profil yoksa otomatik sync et
                var syncResult = await SyncAcademicianProfileAsync(universityUserId, forceSync: true);
                if (syncResult.Success && syncResult.Profile != null)
                {
                    profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
                }
            }
            return profile;
        }
        #endregion
        #region Bulk Sync Operations
        public async Task<BulkProfileSyncResultDto> SyncAcademicianProfilesBulkAsync(BulkProfileSyncRequestDto requestDto)
        {
            var result = new BulkProfileSyncResultDto
            {
                SyncStartedAt = DateTime.UtcNow,
                TotalRequested = requestDto.SyncAll ? 0 : requestDto.UniversityUserIds.Count
            };
            try
            {
                List<string> userIdsToSync;
                if (requestDto.SyncAll)
                {
                    // Sync gereken tüm aktif akademisyen profillerini getir
                    var profilesNeedingSync = await _academicianStore.GetAcademicianProfilesRequiringSyncAsync();
                    userIdsToSync = profilesNeedingSync.Select(p => p.UniversityUserId).ToList();
                    result.TotalRequested = userIdsToSync.Count;
                }
                else
                {
                    userIdsToSync = requestDto.UniversityUserIds;
                }
                foreach (var userId in userIdsToSync)
                {
                    var syncResult = await SyncAcademicianProfileAsync(userId, requestDto.ForceSync);
                    result.Results.Add(syncResult);
                    if (syncResult.Success)
                    {
                        if (syncResult.Action == "NoChange")
                            result.NoChangeCount++;
                        else
                            result.SuccessCount++;
                    }
                    else
                    {
                        result.ErrorCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk profile sync");
                // Kalan itemler için hata sonucu ekle
                result.ErrorCount = result.TotalRequested - result.Results.Count;
            }
            result.SyncCompletedAt = DateTime.UtcNow;
            return result;
        }
        public async Task<BulkProfileSyncResultDto> SyncAllAcademicianProfilesAsync(bool forceSync = false)
        {
            var requestDto = new BulkProfileSyncRequestDto
            {
                SyncAll = true,
                ForceSync = forceSync
            };
            return await SyncAcademicianProfilesBulkAsync(requestDto);
        }
        public async Task<List<AcademicianProfileDto>> GetProfilesRequiringSyncAsync(int maxHoursOld = 24)
        {
            var cutoffDate = DateTime.UtcNow.AddHours(-maxHoursOld);
            var profiles = await _academicianStore.GetAcademicianProfilesRequiringSyncAsync(cutoffDate);
            return profiles.Adapt<List<AcademicianProfileDto>>();
        }
        #endregion
        #region Form Assignment Logic
        public async Task<List<AssignedFormDto>> GetActiveFormsForAcademicCadreAsync(string academicCadre)
        {
            var activeForms = await _formStore.GetEvaluationFormsByStatusAsync("Active");
            var assignedForms = new List<AssignedFormDto>();
            foreach (var form in activeForms)
            {
                // Form bu akademik kadro için uygun mu kontrol et
                if (await IsFormApplicableToAcademicCadreAsync(form, academicCadre))
                {
                    var assignedForm = form.Adapt<AssignedFormDto>();
                    assignedForm.FormStatus = form.Status;
                    assignedForms.Add(assignedForm);
                }
            }
            return assignedForms;
        }
        public async Task<bool> IsFormAssignedToAcademicianAsync(string universityUserId, string formId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            if (profile?.AcademicCadre == null)
                return false;
            var form = await _formStore.GetEvaluationFormByIdAsync(formId);
            if (form == null)
                return false;
            return await IsFormApplicableToAcademicCadreAsync(form, profile.AcademicCadre);
        }
        public async Task<List<string>> GetEligibleAcademicCadresForFormAsync(string formId)
        {
            var form = await _formStore.GetEvaluationFormByIdAsync(formId);
            if (form == null || string.IsNullOrEmpty(form.ApplicableAcademicCadresJson))
                return new List<string>();
            try
            {
                return JsonSerializer.Deserialize<List<string>>(form.ApplicableAcademicCadresJson) ?? new List<string>();
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "Failed to parse ApplicableAcademicCadresJson for form {FormId}", formId);
                return new List<string>();
            }
        }
        private Task<bool> IsFormApplicableToAcademicCadreAsync(EvaluationFormEntity form, string academicCadre)
        {
            if (string.IsNullOrEmpty(form.ApplicableAcademicCadresJson))
                return Task.FromResult(false);
            try
            {
                var applicableCadres = JsonSerializer.Deserialize<List<string>>(form.ApplicableAcademicCadresJson);
                return Task.FromResult(applicableCadres?.Contains(academicCadre) == true);
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "Failed to parse ApplicableAcademicCadresJson for form {FormId}", form.Id);
                return Task.FromResult(false);
            }
        }
        #endregion

        #region Pagination Methods

        /// <summary>
        /// Akademisyene atanmış formları sayfalanmış olarak getir
        /// </summary>
        public async Task<PagedListDto<AssignedFormDto>> GetAssignedFormsAsync(string universityUserId, PagedListCo<GetAcademicianFormsCo> co)
        {
            // Şu an için mevcut GetAssignedFormsAsync metodunu kullanarak mock pagination yapıyoruz
            // Gerçek implementasyon store katmanında yapılacak
            var allForms = await GetAssignedFormsAsync(universityUserId);

            // Filtreleme uygula
            var filteredForms = allForms.AsQueryable();

            if (!string.IsNullOrEmpty(co.Criteria?.NameContains))
            {
                filteredForms = filteredForms.Where(f => f.Name != null && f.Name.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(co.Criteria?.Status))
            {
                filteredForms = filteredForms.Where(f => f.Status == co.Criteria!.Status);
            }

            if (!string.IsNullOrEmpty(co.Criteria?.SubmissionStatus))
            {
                filteredForms = filteredForms.Where(f => f.SubmissionStatus != null && f.SubmissionStatus.Status == co.Criteria!.SubmissionStatus);
            }

            if (co.Criteria?.OnlyApproachingDeadlines == true)
            {
                filteredForms = filteredForms.Where(f => f.IsDeadlineApproaching == true);
            }

            if (co.Criteria?.OnlyActive == true)
            {
                filteredForms = filteredForms.Where(f => f.Status == "Active");
            }

            // Sıralama uygula
            if (!string.IsNullOrEmpty(co.Sort))
            {
                filteredForms = co.Sort.ToLower() switch
                {
                    "name" => filteredForms.OrderBy(f => f.Name),
                    "name_desc" => filteredForms.OrderByDescending(f => f.Name),
                    "deadline" => filteredForms.OrderBy(f => f.SubmissionDeadline),
                    "deadline_desc" => filteredForms.OrderByDescending(f => f.SubmissionDeadline),
                    _ => filteredForms.OrderByDescending(f => f.CreatedAt)
                };
            }
            else
            {
                filteredForms = filteredForms.OrderByDescending(f => f.CreatedAt);
            }

            // Pagination uygula
            var totalCount = filteredForms.Count();
            var pagedData = filteredForms
                .Skip(co.Pager.Skip)
                .Take(co.Pager.Size)
                .ToList();

            return new PagedListDto<AssignedFormDto>
            {
                Count = totalCount,
                Page = co.Pager.Page,
                Size = co.Pager.Size,
                Data = pagedData
            };
        }

        /// <summary>
        /// Akademisyenin submission'larını sayfalanmış olarak getir
        /// </summary>
        public Task<PagedListDto<SubmissionDto>> GetSubmissionsAsync(string universityUserId, PagedListCo<GetAcademicianSubmissionsCo> co)
        {
            // Bu metod şu an için mock implementation
            // Gerçek implementasyon submission store'da yapılacak
            var mockSubmissions = new List<SubmissionDto>();

            return Task.FromResult(new PagedListDto<SubmissionDto>
            {
                Count = 0,
                Page = co.Pager.Page,
                Size = co.Pager.Size,
                Data = mockSubmissions
            });
        }

        #endregion
        #region Submission Status Operations
        public async Task<SubmissionStatusDto?> GetSubmissionStatusAsync(string universityUserId, string formId)
        {
            var profile = await _academicianStore.GetAcademicianProfileWithSubmissionsAsync(universityUserId);
            if (profile?.Submissions == null)
                return new SubmissionStatusDto { Status = "NotStarted" };
            var formIdMap = await _formStore.IdConvertForEvaluationForm(new[] { formId });
            if (!formIdMap.TryGetValue(formId, out var formAutoIncrementId))
                return new SubmissionStatusDto { Status = "NotStarted" };
            var submission = profile.Submissions.FirstOrDefault(s => s.EvaluationFormAutoIncrementId == formAutoIncrementId);
            if (submission == null)
                return new SubmissionStatusDto { Status = "NotStarted" };
            return new SubmissionStatusDto
            {
                SubmissionId = submission.Id,
                Status = submission.Status,
                SubmittedAt = submission.SubmittedAt,
                LastModifiedAt = submission.UpdatedAt,
                ApprovedAt = submission.ApprovedAt,
                ApprovedByControllerUserId = submission.ApprovedByControllerUserId,
                RejectedAt = submission.RejectedAt,
                RejectedByControllerUserId = submission.RejectedByControllerUserId,
                ApprovalComments = submission.ApprovalComments,
                RejectionComments = submission.RejectionComments
            };
        }
        public async Task<List<SubmissionStatusDto>> GetAllSubmissionStatusesAsync(string universityUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileWithSubmissionsAsync(universityUserId);
            if (profile?.Submissions == null)
                return new List<SubmissionStatusDto>();
            return profile.Submissions.Select(s => new SubmissionStatusDto
            {
                SubmissionId = s.Id,
                Status = s.Status,
                SubmittedAt = s.SubmittedAt,
                LastModifiedAt = s.UpdatedAt,
                ApprovedAt = s.ApprovedAt,
                ApprovedByControllerUserId = s.ApprovedByControllerUserId,
                RejectedAt = s.RejectedAt,
                RejectedByControllerUserId = s.RejectedByControllerUserId,
                ApprovalComments = s.ApprovalComments,
                RejectionComments = s.RejectionComments
            }).ToList();
        }
        public async Task<bool> HasPendingSubmissionsAsync(string universityUserId)
        {
            var statuses = await GetAllSubmissionStatusesAsync(universityUserId);
            return statuses.Any(s => s.Status == "Draft" || s.Status == "Submitted" || s.Status == "UnderReview");
        }
        public async Task<int> GetCompletedSubmissionCountAsync(string universityUserId)
        {
            var statuses = await GetAllSubmissionStatusesAsync(universityUserId);
            return statuses.Count(s => s.Status == "Approved");
        }
        #endregion
        #region Quick Actions
        public async Task<List<QuickActionDto>> GetAvailableQuickActionsAsync(string universityUserId)
        {
            var assignedForms = await GetAssignedFormsAsync(universityUserId);
            var quickActions = new List<QuickActionDto>();
            foreach (var form in assignedForms)
            {
                var action = await GetQuickActionForFormAsync(universityUserId, form.Id!);
                if (action != null)
                {
                    quickActions.Add(action);
                }
            }
            return quickActions;
        }
        public async Task<QuickActionDto?> GetQuickActionForFormAsync(string universityUserId, string formId)
        {
            var submissionStatus = await GetSubmissionStatusAsync(universityUserId, formId);
            if (submissionStatus == null)
                return null;
            var canEdit = await CanAcademicianEditSubmissionAsync(universityUserId, formId);
            var canSubmit = await CanAcademicianSubmitFormAsync(universityUserId, formId);
            return submissionStatus.Status switch
            {
                "NotStarted" => new QuickActionDto
                {
                    ActionType = "StartSubmission",
                    FormId = formId,
                    ActionText = "Start Submission",
                    IsEnabled = canEdit
                },
                "Draft" => new QuickActionDto
                {
                    ActionType = "ContinueSubmission",
                    FormId = formId,
                    SubmissionId = submissionStatus.SubmissionId,
                    ActionText = "Continue Draft",
                    IsEnabled = canEdit
                },
                "Submitted" or "UnderReview" or "Approved" or "Rejected" => new QuickActionDto
                {
                    ActionType = "ViewSubmission",
                    FormId = formId,
                    SubmissionId = submissionStatus.SubmissionId,
                    ActionText = "View Submission",
                    IsEnabled = true
                },
                _ => null
            };
        }

        // Pagination Method for Quick Actions
        public async Task<PagedListDto<QuickActionDto>> GetAvailableQuickActionsAsync(string universityUserId, PagedListCo<GetStatusFilterCo> co)
        {
            var allQuickActions = await GetAvailableQuickActionsAsync(universityUserId);

            // Filtreleme uygula
            var filteredActions = allQuickActions.AsQueryable();

            if (!string.IsNullOrEmpty(co.Criteria?.NameContains))
            {
                filteredActions = filteredActions.Where(a =>
                    (a.ActionText != null && a.ActionText.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase)) ||
                    (a.ActionType != null && a.ActionType.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase)));
            }

            if (co.Criteria?.OnlyActive == true)
            {
                filteredActions = filteredActions.Where(a => a.IsEnabled);
            }

            // Sıralama uygula
            if (!string.IsNullOrEmpty(co.Sort))
            {
                filteredActions = co.Sort.ToLower() switch
                {
                    "type" => filteredActions.OrderBy(a => a.ActionType),
                    "type_desc" => filteredActions.OrderByDescending(a => a.ActionType),
                    "text" => filteredActions.OrderBy(a => a.ActionText),
                    "text_desc" => filteredActions.OrderByDescending(a => a.ActionText),
                    "enabled" => filteredActions.OrderBy(a => a.IsEnabled),
                    "enabled_desc" => filteredActions.OrderByDescending(a => a.IsEnabled),
                    _ => filteredActions.OrderBy(a => a.ActionType)
                };
            }
            else
            {
                filteredActions = filteredActions.OrderBy(a => a.ActionType);
            }

            // Pagination uygula
            var totalCount = filteredActions.Count();
            var pagedData = filteredActions
                .Skip(co.Pager.Skip)
                .Take(co.Pager.Size)
                .ToList();

            return new PagedListDto<QuickActionDto>
            {
                Count = totalCount,
                Page = co.Pager.Page,
                Size = co.Pager.Size,
                Data = pagedData
            };
        }
        #endregion
        #region Statistics and Analytics
        public async Task<Dictionary<string, int>> GetSubmissionStatisticsByStatusAsync(string universityUserId)
        {
            var statuses = await GetAllSubmissionStatusesAsync(universityUserId);
            return statuses.GroupBy(s => s.Status)
                          .ToDictionary(g => g.Key, g => g.Count());
        }
        public async Task<double> GetOverallCompletionPercentageAsync(string universityUserId)
        {
            var statistics = await GetDashboardStatisticsAsync(universityUserId);
            return statistics.OverallCompletionPercentage;
        }
        public async Task<List<AssignedFormDto>> GetFormsWithApproachingDeadlinesAsync(string universityUserId, int daysThreshold = 7)
        {
            var assignedForms = await GetAssignedFormsAsync(universityUserId);
            return assignedForms.Where(f => f.IsDeadlineApproaching &&
                                          f.DaysUntilDeadline <= daysThreshold &&
                                          f.DaysUntilDeadline >= 0).ToList();
        }

        // Pagination Methods for Statistics and Analytics
        public async Task<PagedListDto<SubmissionStatusDto>> GetAllSubmissionStatusesAsync(string universityUserId, PagedListCo<GetStatusFilterCo> co)
        {
            var allStatuses = await GetAllSubmissionStatusesAsync(universityUserId);

            // Filtreleme uygula
            var filteredStatuses = allStatuses.AsQueryable();

            if (!string.IsNullOrEmpty(co.Criteria?.NameContains))
            {
                // Status'ta arama yap
                filteredStatuses = filteredStatuses.Where(s => s.Status.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase));
            }

            if (co.Criteria?.CreatedAfter.HasValue == true)
            {
                filteredStatuses = filteredStatuses.Where(s => s.SubmittedAt >= co.Criteria!.CreatedAfter.Value);
            }

            if (co.Criteria?.CreatedBefore.HasValue == true)
            {
                filteredStatuses = filteredStatuses.Where(s => s.SubmittedAt <= co.Criteria!.CreatedBefore.Value);
            }

            // Sıralama uygula
            if (!string.IsNullOrEmpty(co.Sort))
            {
                filteredStatuses = co.Sort.ToLower() switch
                {
                    "status" => filteredStatuses.OrderBy(s => s.Status),
                    "status_desc" => filteredStatuses.OrderByDescending(s => s.Status),
                    "submitted" => filteredStatuses.OrderBy(s => s.SubmittedAt),
                    "submitted_desc" => filteredStatuses.OrderByDescending(s => s.SubmittedAt),
                    _ => filteredStatuses.OrderByDescending(s => s.LastModifiedAt)
                };
            }
            else
            {
                filteredStatuses = filteredStatuses.OrderByDescending(s => s.LastModifiedAt);
            }

            // Pagination uygula
            var totalCount = filteredStatuses.Count();
            var pagedData = filteredStatuses
                .Skip(co.Pager.Skip)
                .Take(co.Pager.Size)
                .ToList();

            return new PagedListDto<SubmissionStatusDto>
            {
                Count = totalCount,
                Page = co.Pager.Page,
                Size = co.Pager.Size,
                Data = pagedData
            };
        }

        public async Task<PagedListDto<AssignedFormDto>> GetFormsWithApproachingDeadlinesAsync(string universityUserId, int daysThreshold, PagedListCo<GetStatusFilterCo> co)
        {
            var allForms = await GetFormsWithApproachingDeadlinesAsync(universityUserId, daysThreshold);

            // Filtreleme uygula
            var filteredForms = allForms.AsQueryable();

            if (!string.IsNullOrEmpty(co.Criteria?.NameContains))
            {
                filteredForms = filteredForms.Where(f => f.Name != null && f.Name.Contains(co.Criteria!.NameContains, StringComparison.OrdinalIgnoreCase));
            }

            if (co.Criteria?.OnlyActive == true)
            {
                filteredForms = filteredForms.Where(f => f.Status == "Active");
            }

            if (co.Criteria?.CreatedAfter.HasValue == true)
            {
                filteredForms = filteredForms.Where(f => f.CreatedAt >= co.Criteria!.CreatedAfter.Value);
            }

            if (co.Criteria?.CreatedBefore.HasValue == true)
            {
                filteredForms = filteredForms.Where(f => f.CreatedAt <= co.Criteria!.CreatedBefore.Value);
            }

            // Sıralama uygula
            if (!string.IsNullOrEmpty(co.Sort))
            {
                filteredForms = co.Sort.ToLower() switch
                {
                    "name" => filteredForms.OrderBy(f => f.Name),
                    "name_desc" => filteredForms.OrderByDescending(f => f.Name),
                    "deadline" => filteredForms.OrderBy(f => f.SubmissionDeadline),
                    "deadline_desc" => filteredForms.OrderByDescending(f => f.SubmissionDeadline),
                    "days" => filteredForms.OrderBy(f => f.DaysUntilDeadline),
                    "days_desc" => filteredForms.OrderByDescending(f => f.DaysUntilDeadline),
                    _ => filteredForms.OrderBy(f => f.DaysUntilDeadline)
                };
            }
            else
            {
                filteredForms = filteredForms.OrderBy(f => f.DaysUntilDeadline);
            }

            // Pagination uygula
            var totalCount = filteredForms.Count();
            var pagedData = filteredForms
                .Skip(co.Pager.Skip)
                .Take(co.Pager.Size)
                .ToList();

            return new PagedListDto<AssignedFormDto>
            {
                Count = totalCount,
                Page = co.Pager.Page,
                Size = co.Pager.Size,
                Data = pagedData
            };
        }

        #endregion
        #region Validation and Business Rules
        public async Task<bool> CanAcademicianAccessFormAsync(string universityUserId, string formId)
        {
            return await IsFormAssignedToAcademicianAsync(universityUserId, formId);
        }
        public async Task<bool> CanAcademicianEditSubmissionAsync(string universityUserId, string formId)
        {
            var submissionStatus = await GetSubmissionStatusAsync(universityUserId, formId);
            if (submissionStatus == null)
                return false;
            // NotStarted veya Draft durumunda düzenlenebilir
            return submissionStatus.Status == "NotStarted" || submissionStatus.Status == "Draft";
        }
        public async Task<bool> CanAcademicianSubmitFormAsync(string universityUserId, string formId)
        {
            var submissionStatus = await GetSubmissionStatusAsync(universityUserId, formId);
            if (submissionStatus == null)
                return false;
            // Draft durumunda gönderilebilir
            return submissionStatus.Status == "Draft";
        }
        public async Task<bool> IsAcademicianProfileActiveAsync(string universityUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            return profile?.IsActive == true && !profile.Deleted && !profile.Disabled;
        }
        #endregion
        #region Cache Management
        public async Task<bool> RefreshAcademicianCacheAsync(string universityUserId)
        {
            // Cache'i yenilemek için profili zorla sync et
            var syncResult = await SyncAcademicianProfileAsync(universityUserId, forceSync: true);
            return syncResult.Success;
        }
        public Task<bool> ClearAcademicianCacheAsync(string universityUserId)
        {
            // Cache servisine bağlı olarak implement edilecek
            // Şimdilik sadece true döndür
            return Task.FromResult(true);
        }
        public async Task<bool> WarmupAcademicianCacheAsync(string universityUserId)
        {
            // Cache'i ısıtmak için dashboard verilerini önceden yükle
            try
            {
                await GetAcademicianDashboardAsync(universityUserId);
                return true;
            }
            catch
            {
                return false;
            }
        }
        #endregion
        #region Administrative Operations
        public async Task<bool> ActivateAcademicianProfileAsync(string universityUserId, string activatedByUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            if (profile == null)
                return false;
            profile.IsActive = true;
            profile.Disabled = false;
            profile.UpdatedByUserId = activatedByUserId;
            profile.SyncNotes = $"Activated by {activatedByUserId} at {DateTime.UtcNow}";
            return await _academicianStore.UpdateAcademicianProfileAsync(profile);
        }
        public async Task<bool> DeactivateAcademicianProfileAsync(string universityUserId, string deactivatedByUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            if (profile == null)
                return false;
            profile.IsActive = false;
            profile.UpdatedByUserId = deactivatedByUserId;
            profile.SyncNotes = $"Deactivated by {deactivatedByUserId} at {DateTime.UtcNow}";
            return await _academicianStore.UpdateAcademicianProfileAsync(profile);
        }
        public async Task<bool> UpdateAcademicianProfileNotesAsync(string universityUserId, string notes, string updatedByUserId)
        {
            var profile = await _academicianStore.GetAcademicianProfileByUniversityUserIdAsync(universityUserId);
            if (profile == null)
                return false;
            profile.SyncNotes = notes;
            profile.UpdatedByUserId = updatedByUserId;
            return await _academicianStore.UpdateAcademicianProfileAsync(profile);
        }
        #endregion
    }
}
