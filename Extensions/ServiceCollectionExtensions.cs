// using AcademicPerformance.Models.Configurations;
// using AcademicPerformance.Services.Interfaces;
// using AcademicPerformance.Services.Implementations;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Options;

// namespace AcademicPerformance.Extensions;

// /// <summary>
// /// Service collection extensions for configuration and monitoring
// /// </summary>
// public static class ServiceCollectionExtensions
// {
//     /// <summary>
//     /// Add database performance configuration
//     /// </summary>
//     public static IServiceCollection AddDatabasePerformanceConfiguration(
//         this IServiceCollection services,
//         IConfiguration configuration)
//     {
//         // Configure DatabasePerformanceConfiguration
//         services.Configure<DatabasePerformanceConfiguration>(
//             configuration.GetSection("DatabasePerformance"));

//         // Register as singleton for direct injection
//         services.AddSingleton<DatabasePerformanceConfiguration>(provider =>
//         {
//             var options = provider.GetRequiredService<IOptions<DatabasePerformanceConfiguration>>();
//             return options.Value;
//         });

//         return services;
//     }

//     /// <summary>
//     /// Add pagination configuration
//     /// </summary>
//     public static IServiceCollection AddPaginationConfiguration(
//         this IServiceCollection services,
//         IConfiguration configuration)
//     {
//         services.Configure<PaginationConfiguration>(
//             configuration.GetSection("Pagination"));

//         services.AddSingleton<PaginationConfiguration>(provider =>
//         {
//             var options = provider.GetRequiredService<IOptions<PaginationConfiguration>>();
//             return options.Value;
//         });

//         return services;
//     }

//     /// <summary>
//     /// Add Redis cache configuration
//     /// </summary>
//     public static IServiceCollection AddRedisCacheConfiguration(
//         this IServiceCollection services,
//         IConfiguration configuration)
//     {
//         services.Configure<RedisCacheConfiguration>(
//             configuration.GetSection("RedisCache"));

//         services.AddSingleton<RedisCacheConfiguration>(provider =>
//         {
//             var options = provider.GetRequiredService<IOptions<RedisCacheConfiguration>>();
//             return options.Value;
//         });

//         return services;
//     }



//     /// <summary>
//     /// Add all bulk operation configurations and services
//     /// </summary>
//     public static IServiceCollection AddBulkOperationServices(
//         this IServiceCollection services,
//         IConfiguration configuration)
//     {
//         // Performance monitoring removed

//         // Add Redis cache if configured
//         var redisCacheSection = configuration.GetSection("RedisCache");
//         if (redisCacheSection.Exists() && !string.IsNullOrEmpty(redisCacheSection["ConnectionString"]))
//         {
//             services.AddRedisCacheConfiguration(configuration);

//             // Redis cache removed (dependency issue)
//         }
//         else
//         {
//             // Use in-memory cache as fallback
//             services.AddMemoryCache();
//         }

//         return services;
//     }

//     /// <summary>
//     /// Configure environment-specific database performance settings
//     /// </summary>
//     public static IServiceCollection ConfigureEnvironmentSpecificPerformance(
//         this IServiceCollection services,
//         IConfiguration configuration,
//         string environmentName)
//     {
//         services.PostConfigure<DatabasePerformanceConfiguration>(options =>
//         {
//             // Environment-specific batch sizes
//             options.BatchSize = environmentName.ToLowerInvariant() switch
//             {
//                 "development" => 500,
//                 "staging" => 1000,
//                 "production" => 2000,
//                 "docker" => 2000,
//                 _ => 1000
//             };

//             // Environment-specific query timeout
//             options.QueryTimeout = environmentName.ToLowerInvariant() switch
//             {
//                 "development" => 60,
//                 "staging" => 120,
//                 "production" => 300,
//                 "docker" => 180,
//                 _ => 120
//             };

//             // Environment-specific logging
//             options.EnableQueryLogging = environmentName.ToLowerInvariant() switch
//             {
//                 "development" => true,
//                 "staging" => false,
//                 "production" => false,
//                 "docker" => false,
//                 _ => false
//             };

//             options.EnableSensitiveDataLogging = environmentName.ToLowerInvariant() == "development";

//             // Environment-specific retry settings
//             options.MaxRetryCount = environmentName.ToLowerInvariant() switch
//             {
//                 "development" => 2,
//                 "staging" => 3,
//                 "production" => 5,
//                 "docker" => 3,
//                 _ => 3
//             };
//         });

//         return services;
//     }

//     /// <summary>
//     /// Add bulk operation health checks
//     /// </summary>
//     public static IServiceCollection AddBulkOperationHealthChecks(
//         this IServiceCollection services,
//         IConfiguration configuration)
//     {
//         services.AddHealthChecks()
//             .AddCheck<DatabasePerformanceHealthCheck>("database_performance");
//         // Performance monitoring health check removed

//         return services;
//     }
// }

// /// <summary>
// /// Database performance health check
// /// </summary>
// public class DatabasePerformanceHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
// {
//     private readonly DatabasePerformanceConfiguration _config;

//     public DatabasePerformanceHealthCheck(DatabasePerformanceConfiguration config)
//     {
//         _config = config;
//     }

//     public Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
//         Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
//         CancellationToken cancellationToken = default)
//     {
//         var isHealthy = _config.BatchSize > 0 &&
//                        _config.QueryTimeout > 0 &&
//                        _config.MaxRetryCount > 0;

//         var data = new Dictionary<string, object>
//         {
//             { "BatchSize", _config.BatchSize },
//             { "QueryTimeout", _config.QueryTimeout },
//             { "MaxRetryCount", _config.MaxRetryCount },
//             { "EnableQueryLogging", _config.EnableQueryLogging }
//         };

//         return Task.FromResult(isHealthy
//             ? Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
//                 "Database performance configuration is valid", data)
//             : Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
//                 "Database performance configuration is invalid", data: data));
//     }
// }


