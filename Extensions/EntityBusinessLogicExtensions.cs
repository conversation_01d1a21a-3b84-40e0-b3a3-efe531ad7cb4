using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Extensions;

/// <summary>
/// Business logic extensions for common entity operations
/// </summary>
public static class EntityBusinessLogicExtensions
{
    /// <summary>
    /// Apply audit fields to entities that implement IAuditableEntity
    /// </summary>
    public static void ApplyAuditFields<T>(this IEnumerable<T> entities,
        DateTime? timestamp = null, string? userId = null) where T : class
    {
        var now = timestamp ?? DateTime.UtcNow;

        foreach (var entity in entities)
        {
            // Use reflection to set common audit fields
            var entityType = entity.GetType();

            // Set CreatedAt if it exists and is null
            var createdAtProperty = entityType.GetProperty("CreatedAt");
            if (createdAtProperty != null && createdAtProperty.CanWrite)
            {
                var currentValue = createdAtProperty.GetValue(entity);
                if (currentValue == null || (currentValue is DateTime dt && dt == default))
                {
                    createdAtProperty.SetValue(entity, now);
                }
            }

            // Set UpdatedAt if it exists
            var updatedAtProperty = entityType.GetProperty("UpdatedAt");
            if (updatedAtProperty != null && updatedAtProperty.CanWrite)
            {
                updatedAtProperty.SetValue(entity, now);
            }

            // Set CreatedByUserId if provided and property exists
            if (!string.IsNullOrEmpty(userId))
            {
                var createdByProperty = entityType.GetProperty("CreatedByUserId");
                if (createdByProperty != null && createdByProperty.CanWrite)
                {
                    var currentValue = createdByProperty.GetValue(entity);
                    if (currentValue == null || string.IsNullOrEmpty(currentValue.ToString()))
                    {
                        createdByProperty.SetValue(entity, userId);
                    }
                }

                var updatedByProperty = entityType.GetProperty("UpdatedByUserId");
                if (updatedByProperty != null && updatedByProperty.CanWrite)
                {
                    updatedByProperty.SetValue(entity, userId);
                }
            }
        }
    }

    /// <summary>
    /// Apply academician-specific business logic
    /// </summary>
    public static void ApplyAcademicianBusinessLogic(
        this IEnumerable<AcademicianProfileEntity> entities)
    {
        var now = DateTime.UtcNow;

        foreach (var entity in entities)
        {
            // Audit fields
            if (entity.CreatedAt == default) entity.CreatedAt = now;
            entity.UpdatedAt = now;
            entity.LastSyncedAt = now;

            // Business logic fields
            entity.IsActive = true;
            entity.Deleted = false;
            entity.Disabled = false;

            // Calculate full name
            entity.FullName = $"{entity.Name?.Trim()} {entity.Surname?.Trim()}".Trim();

            // Sync status property removed from entity

            // Validate required fields
            if (string.IsNullOrWhiteSpace(entity.UniversityUserId))
            {
                throw new ValidationException($"UniversityUserId is required for academician: {entity.FullName}");
            }

            if (string.IsNullOrWhiteSpace(entity.Name))
            {
                throw new ValidationException($"Name is required for UniversityUserId: {entity.UniversityUserId}");
            }

            if (string.IsNullOrWhiteSpace(entity.Surname))
            {
                throw new ValidationException($"Surname is required for UniversityUserId: {entity.UniversityUserId}");
            }
        }
    }

    /// <summary>
    /// Apply department performance calculations and business logic
    /// </summary>
    public static void ApplyPerformanceCalculations(
        this IEnumerable<DepartmentPerformanceEntity> entities)
    {
        var now = DateTime.UtcNow;

        foreach (var entity in entities)
        {
            // Audit fields
            if (entity.CreatedAt == default) entity.CreatedAt = now;
            entity.UpdatedAt = now;
            // CalculatedAt property removed from entity

            // Calculate overall score using weighted average
            entity.OverallScore = CalculateOverallScore(entity);

            // Determine performance status based on score
            entity.Status = DeterminePerformanceStatus(entity.OverallScore);

            // PerformanceGrade property removed from entity

            // Validate scores are within valid range
            ValidatePerformanceScores(entity);
        }
    }

    /// <summary>
    /// Apply staff competency evaluation business logic
    /// </summary>
    public static void ApplyStaffCompetencyBusinessLogic(
        this IEnumerable<StaffCompetencyEvaluationEntity> entities, string? userId = null)
    {
        var now = DateTime.UtcNow;

        foreach (var entity in entities)
        {
            // Audit fields removed from StaffCompetencyEvaluationEntity (not in EntityBaseModel)

            // Business logic fields (IsActive property removed)
            entity.Disabled = false;

            // Set default status if not provided
            if (string.IsNullOrEmpty(entity.Status))
            {
                entity.Status = "Draft";
            }

            // IsEditable property removed from entity

            // Validate required fields
            if (string.IsNullOrWhiteSpace(entity.AcademicianUniveristyUserId))
            {
                throw new ValidationException("AcademicianUniveristyUserId is required for staff competency evaluation");
            }
        }
    }

    /// <summary>
    /// Apply feedback business logic
    /// </summary>
    public static void ApplyFeedbackBusinessLogic(
        this IEnumerable<SubmissionFeedbackEntity> entities, string? userId = null)
    {
        var now = DateTime.UtcNow;

        foreach (var entity in entities)
        {
            // Audit fields
            if (entity.CreatedAt == default) entity.CreatedAt = now;
            entity.UpdatedAt = now;

            if (!string.IsNullOrEmpty(userId))
            {
                if (string.IsNullOrEmpty(entity.CreatedByUserId)) entity.CreatedByUserId = userId;
                entity.UpdatedByUserId = userId;
            }

            // Business logic fields
            entity.IsActive = true;

            // Set default feedback type if not provided
            if (string.IsNullOrEmpty(entity.FeedbackType))
            {
                entity.FeedbackType = "General";
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(entity.SubmissionId))
            {
                throw new ValidationException("SubmissionId is required for feedback");
            }
        }
    }

    /// <summary>
    /// Calculate overall performance score using weighted average
    /// </summary>
    private static decimal CalculateOverallScore(DepartmentPerformanceEntity entity)
    {
        var weights = new Dictionary<string, decimal>
        {
            { "Research", 0.4m },
            { "Teaching", 0.3m },
            { "Service", 0.2m },
            { "Innovation", 0.1m }
        };

        var totalScore = 0m;
        var totalWeight = 0m;

        // Research performance
        if (entity.ResearchPerformance > 0)
        {
            totalScore += entity.ResearchPerformance * weights["Research"];
            totalWeight += weights["Research"];
        }

        // Publication performance
        if (entity.PublicationPerformance > 0)
        {
            totalScore += entity.PublicationPerformance * weights["Teaching"];
            totalWeight += weights["Teaching"];
        }

        // Student satisfaction score
        if (entity.StudentSatisfactionScore > 0)
        {
            totalScore += entity.StudentSatisfactionScore * weights["Service"];
            totalWeight += weights["Service"];
        }

        // Infrastructure score
        if (entity.InfrastructureScore > 0)
        {
            totalScore += entity.InfrastructureScore * weights["Innovation"];
            totalWeight += weights["Innovation"];
        }

        // Return weighted average, or 0 if no scores provided
        return totalWeight > 0 ? Math.Round(totalScore / totalWeight, 2) : 0m;
    }

    /// <summary>
    /// Determine performance status based on overall score
    /// </summary>
    private static string DeterminePerformanceStatus(decimal overallScore)
    {
        return overallScore switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good",
            >= 70 => "Satisfactory",
            >= 60 => "NeedsImprovement",
            _ => "Poor"
        };
    }

    /// <summary>
    /// Determine performance grade based on overall score
    /// </summary>
    private static string DeterminePerformanceGrade(decimal overallScore)
    {
        return overallScore switch
        {
            >= 95 => "A+",
            >= 90 => "A",
            >= 85 => "A-",
            >= 80 => "B+",
            >= 75 => "B",
            >= 70 => "B-",
            >= 65 => "C+",
            >= 60 => "C",
            >= 55 => "C-",
            >= 50 => "D",
            _ => "F"
        };
    }

    /// <summary>
    /// Validate performance scores are within valid range
    /// </summary>
    private static void ValidatePerformanceScores(DepartmentPerformanceEntity entity)
    {
        var scores = new[]
        {
            ("ResearchPerformance", entity.ResearchPerformance),
            ("PublicationPerformance", entity.PublicationPerformance),
            ("StudentSatisfactionScore", entity.StudentSatisfactionScore),
            ("InfrastructureScore", entity.InfrastructureScore)
        };

        foreach (var (name, score) in scores)
        {
            if (score < 0 || score > 100)
            {
                throw new ValidationException($"{name} must be between 0 and 100. Current value: {score}");
            }
        }
    }

    /// <summary>
    /// Portfolio verification entity business logic uygula
    /// </summary>
    public static void ApplyPortfolioVerificationBusinessLogic(this CoursePortfolioVerificationEntity entity)
    {
        // Verification status validation
        var validStatuses = new[] { "Pending", "InReview", "Approved", "Rejected", "RequiresRevision" };
        if (!validStatuses.Contains(entity.VerificationStatus))
            entity.VerificationStatus = "Pending";

        // Score, Priority, VerifiedAt properties removed from entity
        // Basic validation completed

        // Ensure required fields are not empty
        if (string.IsNullOrEmpty(entity.CourseId))
            throw new ArgumentException("CourseId cannot be empty for portfolio verification");

        if (string.IsNullOrEmpty(entity.StudentId))
            throw new ArgumentException("StudentId cannot be empty for portfolio verification");
    }

    /// <summary>
    /// Criterion feedback entity business logic uygula
    /// </summary>
    public static void ApplyCriterionFeedbackBusinessLogic(this CriterionFeedbackEntity entity)
    {
        // Score validation (0-100 range)
        if (entity.Score < 0) entity.Score = 0;
        if (entity.Score > 100) entity.Score = 100;

        // Status validation
        var validStatuses = new[] { "Draft", "Submitted", "Reviewed", "Approved" };
        if (!validStatuses.Contains(entity.Status))
            entity.Status = "Draft";

        // FeedbackType property removed from entity

        // Ensure required fields are not empty
        if (string.IsNullOrEmpty(entity.CriterionLinkId))
            throw new ArgumentException("CriterionLinkId cannot be empty for criterion feedback");

        if (string.IsNullOrEmpty(entity.SubmissionId))
            throw new ArgumentException("SubmissionId cannot be empty for criterion feedback");

        // IsActive property removed from entity
    }
}
