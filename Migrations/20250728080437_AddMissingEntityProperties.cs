﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class AddMissingEntityProperties : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SubmissionAudits");

            migrationBuilder.AddColumn<decimal>(
                name: "Score",
                table: "SubmissionFeedbacks",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompetencyId",
                table: "StaffCompetencyEvaluations",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "Score",
                table: "StaffCompetencyEvaluations",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StaffId",
                table: "StaffCompetencyEvaluations",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "Score",
                table: "CriterionFeedbacks",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SubmissionId",
                table: "CriterionFeedbacks",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CourseId",
                table: "CoursePortfolioVerifications",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "StudentId",
                table: "CoursePortfolioVerifications",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VerificationStatus",
                table: "CoursePortfolioVerifications",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Score",
                table: "SubmissionFeedbacks");

            migrationBuilder.DropColumn(
                name: "CompetencyId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "Score",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "StaffId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "Score",
                table: "CriterionFeedbacks");

            migrationBuilder.DropColumn(
                name: "SubmissionId",
                table: "CriterionFeedbacks");

            migrationBuilder.DropColumn(
                name: "CourseId",
                table: "CoursePortfolioVerifications");

            migrationBuilder.DropColumn(
                name: "StudentId",
                table: "CoursePortfolioVerifications");

            migrationBuilder.DropColumn(
                name: "VerificationStatus",
                table: "CoursePortfolioVerifications");

            migrationBuilder.CreateTable(
                name: "SubmissionAudits",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicSubmissionAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    DurationMs = table.Column<long>(type: "bigint", nullable: true),
                    EntityId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    EntityType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    IsSuccessful = table.Column<bool>(type: "boolean", nullable: false),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    NewValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    OldValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PerformedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PerformedByUserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    PerformedByUserName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    PerformedByUserRole = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RequestId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    SessionId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    UserAgent = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubmissionAudits", x => x.Id);
                    table.CheckConstraint("CK_SubmissionAudit_DurationMs", "DurationMs >= 0");
                    table.ForeignKey(
                        name: "FK_SubmissionAudits_AcademicSubmissions_AcademicSubmissionAuto~",
                        column: x => x.AcademicSubmissionAutoIncrementId,
                        principalTable: "AcademicSubmissions",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_AcademicSubmissionAutoIncrementId",
                table: "SubmissionAudits",
                column: "AcademicSubmissionAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_AcademicSubmissionAutoIncrementId_Performe~",
                table: "SubmissionAudits",
                columns: new[] { "AcademicSubmissionAutoIncrementId", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Action",
                table: "SubmissionAudits",
                column: "Action");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Action_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "Action", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_AutoIncrementId",
                table: "SubmissionAudits",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Category",
                table: "SubmissionAudits",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_Category_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "Category", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_EntityType",
                table: "SubmissionAudits",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_EntityType_EntityId_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "EntityType", "EntityId", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_IsSuccessful",
                table: "SubmissionAudits",
                column: "IsSuccessful");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_IsSuccessful_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "IsSuccessful", "PerformedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_PerformedAt",
                table: "SubmissionAudits",
                column: "PerformedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_PerformedByUserId",
                table: "SubmissionAudits",
                column: "PerformedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionAudits_PerformedByUserId_PerformedAt",
                table: "SubmissionAudits",
                columns: new[] { "PerformedByUserId", "PerformedAt" });
        }
    }
}
