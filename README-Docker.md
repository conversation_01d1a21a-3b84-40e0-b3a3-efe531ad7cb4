# AcademicPerformance Docker Setup

Bu dokümantasyon AcademicPerformance projesini Docker ile PostgreSQL, MongoDB ve Redis servisleri ile birlikte çalıştırmak için gerekli adımları içerir.

## Gereksinimler

- Docker
- Docker Compose

## Servisler

### 1. PostgreSQL
- **Port**: 5432
- **Veritabanları**: 
  - `academicperformance`
- **Kullanıcı**: postgres
- **Şifre**: postgres (`.env` dosyasından değiştirilebilir)

### 2. MongoDB
- **Port**: 27017
- **Veritabanı**: `ApdysDynamicData`
- **Root Kullanıcı**: admin
- **Root Şifre**: admin123 (`.env` dosyasından değiştirilebilir)

### 3. Redis
- **Port**: 6379
- **Kullanım**: Cache servisi

### 4. AcademicPerformance App
- **Port**: 5122
- **Environment**: Docker
- **Framework**: .NET 8

## Kurulum ve Çalıştırma

###  Environment Variables'ı Ayarlayın
`.env` dosyasını ihtiyaçlarınıza göre düzenleyin:

```bash
# PostgreSQL Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# MongoDB Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=admin123
MONGO_DATABASE=ApdysDynamicData

# OpenIddict Configuration
OPENIDDICT_ISSUER=https://identity.arel.edu.tr
```

### Docker Compose ile Çalıştırın
Uygulama başladığında şu komutu çalıştırın:

```bash
dotnet ef database update -c AcademicPerformanceDbContext
```

```bash
# Tüm servisleri başlat
docker-compose up -d

# Logları takip et
docker-compose logs -f

# Sadece uygulamayı yeniden başlat
docker-compose restart academicperformance
```

###  Servisleri Kontrol Edin
```bash
# Çalışan servisleri listele
docker-compose ps

# Servis durumlarını kontrol et
docker-compose logs postgres
docker-compose logs mongodb
docker-compose logs redis
docker-compose logs academicperformance
```

## Veritabanı Bağlantıları

### PostgreSQL
- **Host**: localhost (Docker dışından) / postgres (Docker içinden)
- **Port**: 5432
- **Databases**: academicperformance
- **Username**: postgres
- **Password**: postgres

### MongoDB
- **Host**: localhost (Docker dışından) / mongodb (Docker içinden)
- **Port**: 27017
- **Database**: ApdysDynamicData
- **Connection String**: `**************************************************************************`

### Redis
- **Host**: localhost (Docker dışından) / redis (Docker içinden)
- **Port**: 6379

## Uygulama Erişimi

- **API**: http://localhost:5122
- **Swagger**: http://localhost:5122/swagger
- **Health Check**: http://localhost:5122/health (eğer implementse)


## Sorun Giderme

### 1. Port Çakışması
Eğer portlar kullanımdaysa, `docker-compose.yml` dosyasındaki port mapping'leri değiştirin:

```yaml
ports:
  - "5433:5432"  # PostgreSQL için farklı port
  - "27018:27017"  # MongoDB için farklı port
  - "6380:6379"  # Redis için farklı port
```

### 2. Veritabanı Bağlantı Hatası
- Servislerin tamamen başladığından emin olun: `docker-compose ps`
- Health check'leri kontrol edin: `docker-compose logs <service-name>`
- Connection string'leri doğrulayın

### 3. MongoDB Authentication Hatası
MongoDB'ye bağlanırken authentication hatası alıyorsanız:
- `authSource=admin` parametresinin connection string'de olduğundan emin olun
- MongoDB container'ını yeniden başlatın: `docker-compose restart mongodb`
