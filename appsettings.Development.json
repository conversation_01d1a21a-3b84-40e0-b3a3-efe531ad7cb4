{"Kestrel": {"Certificates": {"Default": {"Path": "Certs/localhost.pfx", "Password": "1smYfuQbEHBTEB"}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"RlxIdentityShared": "Host=**************;Database=RlxIdentity;Username=readonly_user;Password=**************;Port=6007;Pooling=true;MinPoolSize=2;MaxPoolSize=20;ConnectionLifetime=300;CommandTimeout=30;Timeout=30", "AcademicPerformance": "Host=localhost;Database=academicperformance;Username=********;Password=********;Pooling=true;MinPoolSize=5;MaxPoolSize=50;ConnectionLifetime=300;CommandTimeout=120;Timeout=30;ApplicationName=AcademicPerformance-Dev", "Redis": "localhost:6379"}, "ApdysMongoDb": {"ConnectionString": "**************************************************************************", "DatabaseName": "ApdysDynamicData"}, "RedisCache": {"ConnectionString": "rlx_dev:6006,password=qkpWm2qLJqzVwN", "DefaultExpiration": "00:30:00", "SlidingExpiration": "00:15:00", "AbsoluteExpiration": "12:00:00"}, "DatabasePerformance": {"EnableQueryLogging": true, "EnableSensitiveDataLogging": false, "QueryTimeout": 60, "BatchSize": 500, "MaxRetryCount": 2, "MaxRetryDelay": "00:00:15", "EnableServiceProviderCaching": true, "EnableQuerySplitting": true, "TrackingBehavior": "TrackAll"}, "Pagination": {"DefaultPageSize": 10, "MaxPageSize": 50, "EnableTotalCountOptimization": false, "CachePagedResults": false, "CacheExpiration": "00:05:00"}, "MinIO": {"Endpoint": "localhost:9000", "AccessKey": "apdys-admin", "SecretKey": "apdys-secure-password-2024", "UseSSL": false, "DefaultBucket": "apdys-evidence-files", "MaxFileSize": 10485760, "AllowedExtensions": [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"], "AllowedMimeTypes": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "image/jpeg", "image/png"]}, "OpenIddict": {"Issuer": "https://dev-rlxidentity-api.arel.edu.tr:6010"}, "CorsOrigins": ["http://localhost:3000", "https://apdys.arel.edu.tr"], "ExternalApis": {"OrganizationManagement": {"BaseUrl": "https://dev-organizationmanagement-api.arel.edu.tr:6052/"}, "ArelBridge": {"BaseUrl": "https://localhost:7001"}}, "EntityLog": {"Host": "rlx_dev", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "ExcludeEntities": [""]}, "RequestLog": {"Host": "rlx_dev", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "AcademicPerformance"}, "SystemLog": {"Host": "rlx_dev", "Port": "6004", "Username": "UKjbldeMP7swx72qgIfqtU", "Password": "j8WkyK91Y6yjKS8vHG52lZ", "Enabled": "1", "Module": "AcademicPerformance"}}