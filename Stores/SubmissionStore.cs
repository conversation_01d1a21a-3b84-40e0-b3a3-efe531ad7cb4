using MongoDB.Driver;
using MongoDB.Bson;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Stores
{
    /// <summary>
    /// Submission data access implementation - MongoDB operations
    /// </summary>
    public class SubmissionStore : ISubmissionStore
    {
        private readonly IMongoDbService _mongoDbService;
        private readonly ILogger<SubmissionStore> _logger;
        private readonly IFormStore _formStore;

        public SubmissionStore(IMongoDbService mongoDbService, ILogger<SubmissionStore> logger, IFormStore formStore)
        {
            _mongoDbService = mongoDbService;
            _logger = logger;
            _formStore = formStore;
        }

        #region Submission CRUD Operations

        public async Task<AcademicSubmissionDocument?> GetSubmissionByFormIdAsync(string academicianUserId, string formId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.FormId, formId)
                );

                return await _mongoDbService.GetDocumentAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission by form ID {FormId} for user {UserId}", formId, academicianUserId);
                throw;
            }
        }

        public async Task<AcademicSubmissionDocument?> GetSubmissionByIdAsync(string submissionId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                return await _mongoDbService.GetDocumentAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission by ID {SubmissionId}", submissionId);
                throw;
            }
        }

        public async Task<List<AcademicSubmissionDocument>> GetSubmissionsByAcademicianAsync(string academicianUserId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId);
                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.LastActivityAt);

                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions for user {UserId}", academicianUserId);
                throw;
            }
        }

        public async Task<AcademicSubmissionDocument> CreateSubmissionAsync(AcademicSubmissionDocument document)
        {
            try
            {
                document.CreatedAt = DateTime.UtcNow;
                document.LastActivityAt = DateTime.UtcNow;

                await _mongoDbService.InsertDocumentAsync(document);
                return document;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating submission for form {FormId} and user {UserId}",
                    document.FormId, document.AcademicianUserId);
                throw;
            }
        }

        public async Task<bool> UpdateSubmissionAsync(AcademicSubmissionDocument document)
        {
            try
            {
                document.UpdatedAt = DateTime.UtcNow;
                document.LastActivityAt = DateTime.UtcNow;

                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, document.Id);
                var result = await _mongoDbService.ReplaceDocumentAsync(filter, document);

                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating submission {SubmissionId}", document.Id);
                throw;
            }
        }

        public async Task<bool> DeleteSubmissionAsync(string submissionId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var result = await _mongoDbService.DeleteDocumentAsync(filter);

                return result.DeletedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting submission {SubmissionId}", submissionId);
                throw;
            }
        }

        public async Task<bool> UpdateSubmissionStatusAsync(string submissionId, string status, DateTime? submittedAt = null)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);

                var updateBuilder = Builders<AcademicSubmissionDocument>.Update
                    .Set(s => s.Status, status)
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                if (submittedAt.HasValue)
                {
                    updateBuilder = updateBuilder.Set(s => s.SubmittedAt, submittedAt.Value);
                }

                var result = await _mongoDbService.UpdateDocumentAsync(filter, updateBuilder);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating submission status {SubmissionId} to {Status}", submissionId, status);
                throw;
            }
        }

        #endregion

        #region Criterion Data Operations

        public async Task<bool> UpsertCriterionDataAsync(string submissionId, string criterionLinkId, SubmissionCriterionData criterionData)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);

                // CriteriaData array'inde bu criterionLinkId var mı kontrol et
                var arrayFilter = Builders<AcademicSubmissionDocument>.Filter.And(
                    filter,
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch(s => s.CriteriaData,
                        cd => cd.CriterionLinkId == criterionLinkId)
                );

                var existingDoc = await _mongoDbService.GetDocumentAsync(arrayFilter);

                UpdateDefinition<AcademicSubmissionDocument> update;

                if (existingDoc != null)
                {
                    // Güncelle
                    update = Builders<AcademicSubmissionDocument>.Update
                        .Set("CriteriaData.$.DataEntries", criterionData.DataEntries)
                        .Set("CriteriaData.$.IsCompleted", criterionData.IsCompleted)
                        .Set("CriteriaData.$.LastUpdated", DateTime.UtcNow)
                        .Set("CriteriaData.$.Notes", criterionData.Notes)
                        .Set(s => s.UpdatedAt, DateTime.UtcNow)
                        .Set(s => s.LastActivityAt, DateTime.UtcNow);

                    var result = await _mongoDbService.UpdateDocumentAsync(arrayFilter, update);
                    return result.ModifiedCount > 0;
                }
                else
                {
                    // Ekle
                    criterionData.LastUpdated = DateTime.UtcNow;
                    update = Builders<AcademicSubmissionDocument>.Update
                        .Push(s => s.CriteriaData, criterionData)
                        .Set(s => s.UpdatedAt, DateTime.UtcNow)
                        .Set(s => s.LastActivityAt, DateTime.UtcNow);

                    var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                    return result.ModifiedCount > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error upserting criterion data for submission {SubmissionId}, criterion {CriterionLinkId}",
                    submissionId, criterionLinkId);
                throw;
            }
        }

        public async Task<bool> UpdateCriterionDataEntryAsync(string submissionId, string criterionLinkId, string dataEntryId, CriterionDataEntry dataEntry)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId),
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch(s => s.CriteriaData,
                        cd => cd.CriterionLinkId == criterionLinkId),
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch("CriteriaData.DataEntries",
                        Builders<CriterionDataEntry>.Filter.Eq(de => de.Id, dataEntryId))
                );

                dataEntry.UpdatedAt = DateTime.UtcNow;

                var update = Builders<AcademicSubmissionDocument>.Update
                    .Set("CriteriaData.$[criterion].DataEntries.$[entry]", dataEntry)
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var arrayFilters = new[]
                {
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("criterion.CriterionLinkId", criterionLinkId)),
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("entry.Id", dataEntryId))
                };

                var updateOptions = new UpdateOptions { ArrayFilters = arrayFilters };
                var result = await _mongoDbService.UpdateDocumentAsync(filter, update, updateOptions);

                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating criterion data entry {DataEntryId} for submission {SubmissionId}",
                    dataEntryId, submissionId);
                throw;
            }
        }

        public async Task<bool> DeleteCriterionDataEntryAsync(string submissionId, string criterionLinkId, string dataEntryId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId),
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch(s => s.CriteriaData,
                        cd => cd.CriterionLinkId == criterionLinkId)
                );

                var update = Builders<AcademicSubmissionDocument>.Update
                    .PullFilter("CriteriaData.$.DataEntries",
                        Builders<CriterionDataEntry>.Filter.Eq(de => de.Id, dataEntryId))
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting criterion data entry {DataEntryId} for submission {SubmissionId}",
                    dataEntryId, submissionId);
                throw;
            }
        }

        public async Task<List<CriterionDataEntry>?> GetCriterionDataEntriesAsync(string submissionId, string criterionLinkId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var submission = await _mongoDbService.GetDocumentAsync(filter);

                var criterionData = submission?.CriteriaData?.FirstOrDefault(cd => cd.CriterionLinkId == criterionLinkId);
                return criterionData?.DataEntries;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting criterion data entries for submission {SubmissionId}, criterion {CriterionLinkId}",
                    submissionId, criterionLinkId);
                throw;
            }
        }

        public async Task<bool> UpdateCompletionPercentageAsync(string submissionId, decimal percentage)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var update = Builders<AcademicSubmissionDocument>.Update
                    .Set(s => s.CompletionPercentage, percentage)
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating completion percentage for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        #endregion

        #region Query Operations

        public async Task<List<AcademicSubmissionDocument>> GetSubmissionsByStatusAsync(string academicianUserId, string status)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, status)
                );

                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.LastActivityAt);
                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions by status {Status} for user {UserId}", status, academicianUserId);
                throw;
            }
        }

        public async Task<List<AcademicSubmissionDocument>> GetSubmissionsByDateRangeAsync(string academicianUserId, DateTime startDate, DateTime endDate)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Gte(s => s.CreatedAt, startDate),
                    Builders<AcademicSubmissionDocument>.Filter.Lte(s => s.CreatedAt, endDate)
                );

                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.CreatedAt);
                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions by date range for user {UserId}", academicianUserId);
                throw;
            }
        }

        public async Task<bool> SubmissionExistsAsync(string academicianUserId, string formId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.FormId, formId)
                );

                var count = await _mongoDbService.CountDocumentsAsync(filter);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking submission existence for form {FormId} and user {UserId}", formId, academicianUserId);
                throw;
            }
        }

        public async Task<List<AcademicSubmissionDocument>> GetAllSubmissionsByStatusAsync(string status)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, status);
                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.LastActivityAt);

                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all submissions by status {Status}", status);
                throw;
            }
        }

        public async Task<bool> UpdateLastActivityAsync(string submissionId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var update = Builders<AcademicSubmissionDocument>.Update
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating last activity for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        #endregion

        #region Advanced Search Operations

        public async Task<PagedListDto<AcademicSubmissionDocument>> GetSubmissionsWithAdvancedFilteringAsync(string academicianUserId, PagedListCo<GetAcademicSubmissionsCo> co)
        {
            try
            {
                // Base filter - akademisyen kullanıcısı
                var filters = new List<FilterDefinition<AcademicSubmissionDocument>>
                {
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId)
                };

                // FormNameContains filtresi için form ID'lerini al
                List<string>? formIdsFromNameFilter = null;
                if (co.Criteria?.FormNameContains != null)
                {
                    var formCriteria = new GetEvaluationFormsCo
                    {
                        NameContains = co.Criteria.FormNameContains
                    };
                    var formPagedList = new PagedListCo<GetEvaluationFormsCo>
                    {
                        Criteria = formCriteria,
                        Pager = new PagerCo { Page = 1, Size = 1000 } // Büyük sayfa boyutu ile tüm eşleşen formları al
                    };

                    var matchingForms = await _formStore.GetEvaluationFormsAsync(formPagedList);
                    formIdsFromNameFilter = matchingForms.Data.Select(f => f.Id).ToList();

                    // Eğer hiç form bulunamazsa, boş sonuç döndür
                    if (!formIdsFromNameFilter.Any())
                    {
                        return new PagedListDto<AcademicSubmissionDocument>
                        {
                            Count = 0,
                            Page = co.Pager.Page,
                            Size = co.Pager.Size,
                            Data = new List<AcademicSubmissionDocument>()
                        };
                    }
                }

                // Filtreleme kriterlerini uygula
                if (co.Criteria != null)
                {
                    var criteria = co.Criteria;

                    // Status filtresi
                    if (!string.IsNullOrEmpty(criteria.Status))
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, criteria.Status));
                    }

                    // Multiple statuses filtresi
                    if (criteria.Statuses != null && criteria.Statuses.Any())
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.In(s => s.Status, criteria.Statuses));
                    }

                    // Form ID filtresi - FormNameContains'den gelen ID'lerle birleştir
                    var formIdsToFilter = new List<string>();

                    if (!string.IsNullOrEmpty(criteria.FormId))
                    {
                        formIdsToFilter.Add(criteria.FormId);
                    }

                    if (criteria.FormIds != null && criteria.FormIds.Any())
                    {
                        formIdsToFilter.AddRange(criteria.FormIds);
                    }

                    // FormNameContains'den gelen form ID'lerini ekle
                    if (formIdsFromNameFilter != null)
                    {
                        if (formIdsToFilter.Any())
                        {
                            // Hem form ID hem de form name filtresi varsa, kesişimini al
                            formIdsToFilter = formIdsToFilter.Intersect(formIdsFromNameFilter).ToList();
                        }
                        else
                        {
                            // Sadece form name filtresi varsa, onları kullan
                            formIdsToFilter = formIdsFromNameFilter;
                        }
                    }

                    // Form ID filtrelerini uygula
                    if (formIdsToFilter.Any())
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.In(s => s.FormId, formIdsToFilter));
                    }

                    // Tarih filtreleri - CreatedAt
                    if (criteria.CreatedAfter.HasValue)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Gte(s => s.CreatedAt, criteria.CreatedAfter.Value));
                    }

                    if (criteria.CreatedBefore.HasValue)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Lte(s => s.CreatedAt, criteria.CreatedBefore.Value));
                    }

                    // Tarih filtreleri - UpdatedAt
                    if (criteria.UpdatedAfter.HasValue)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Gte(s => s.UpdatedAt, criteria.UpdatedAfter.Value));
                    }

                    if (criteria.UpdatedBefore.HasValue)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Lte(s => s.UpdatedAt, criteria.UpdatedBefore.Value));
                    }

                    // Tarih filtreleri - SubmittedAt
                    if (criteria.SubmittedAfter.HasValue)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Gte(s => s.SubmittedAt, criteria.SubmittedAfter.Value));
                    }

                    if (criteria.SubmittedBefore.HasValue)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Lte(s => s.SubmittedAt, criteria.SubmittedBefore.Value));
                    }

                    // Boolean filtreleri
                    if (criteria.OnlyDrafts == true)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, "Draft"));
                    }

                    if (criteria.OnlySubmitted == true)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, "Submitted"));
                    }

                    if (criteria.OnlyApproved == true)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, "Approved"));
                    }

                    if (criteria.OnlyRejected == true)
                    {
                        filters.Add(Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, "Rejected"));
                    }
                }

                // Tüm filtreleri birleştir
                var combinedFilter = Builders<AcademicSubmissionDocument>.Filter.And(filters);

                // Toplam kayıt sayısını al
                var totalCount = await _mongoDbService.CountDocumentsAsync(combinedFilter);

                // Sıralama - LastActivityAt'e göre azalan
                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.LastActivityAt);

                // Sayfalama için skip ve limit hesapla
                var skip = (co.Pager.Page - 1) * co.Pager.Size;
                var limit = co.Pager.Size;

                // MongoDB'den veriyi çek
                var submissions = await _mongoDbService.GetDocumentsAsync(combinedFilter, sort);
                var pagedSubmissions = submissions.Skip(skip).Take(limit).ToList();

                return new PagedListDto<AcademicSubmissionDocument>
                {
                    Count = (int)totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size,
                    Data = pagedSubmissions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions with advanced filtering for user {UserId}", academicianUserId);
                throw;
            }
        }

        #endregion

        #region Statistics

        public async Task<Dictionary<string, int>> GetSubmissionStatisticsAsync(string academicianUserId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId);
                var submissions = await _mongoDbService.GetDocumentsAsync(filter);

                var stats = new Dictionary<string, int>
                {
                    ["Total"] = submissions.Count,
                    ["Draft"] = submissions.Count(s => s.Status == "Draft"),
                    ["InProgress"] = submissions.Count(s => s.Status == "InProgress"),
                    ["Submitted"] = submissions.Count(s => s.Status == "Submitted"),
                    ["UnderReview"] = submissions.Count(s => s.Status == "UnderReview")
                };

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission statistics for user {UserId}", academicianUserId);
                throw;
            }
        }

        public async Task<int> GetSubmissionCountByFormAsync(string formId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.FormId, formId);
                return (int)await _mongoDbService.CountDocumentsAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission count for form {FormId}", formId);
                throw;
            }
        }

        #endregion
    }
}
