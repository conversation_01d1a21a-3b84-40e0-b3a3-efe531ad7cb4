using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Bölüm performans veri katmanı interface'i
    /// </summary>
    public interface IDepartmentPerformanceStore
    {
        #region CRUD Operations

        /// <summary>
        /// Bölüm performans entity'si oluştur
        /// </summary>
        /// <param name="entity">Oluşturulacak entity</param>
        /// <returns>Oluşturulan entity</returns>
        Task<DepartmentPerformanceEntity> CreateAsync(DepartmentPerformanceEntity entity);

        /// <summary>
        /// Bölüm performans entity'sini güncelle
        /// </summary>
        /// <param name="entity">Güncellenecek entity</param>
        /// <returns>Güncelleme başarılı mı</returns>
        Task<bool> UpdateAsync(DepartmentPerformanceEntity entity);

        /// <summary>
        /// Bölüm performans entity'sini sil
        /// </summary>
        /// <param name="id">Entity ID'si</param>
        /// <returns>Silme başarılı mı</returns>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// Bölüm performans entity'sini ID ile getir
        /// </summary>
        /// <param name="id">Entity ID'si</param>
        /// <returns>Bulunan entity</returns>
        Task<DepartmentPerformanceEntity?> GetByIdAsync(string id);

        /// <summary>
        /// Bölüm performans entity'lerini filtreli getir
        /// </summary>
        /// <param name="co">Filtreleme ve sayfalama parametreleri</param>
        /// <returns>Sayfalanmış entity listesi</returns>
        Task<PagedListDto<DepartmentPerformanceEntity>> GetPagedAsync(
            PagedListCo<DepartmentPerformanceFilterCo> co);

        #endregion

        #region Query Operations

        /// <summary>
        /// Bölümün belirli dönemdeki performans kaydını getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Performans entity'si</returns>
        Task<DepartmentPerformanceEntity?> GetByDepartmentAndPeriodAsync(
            string departmentId,
            string period);

        /// <summary>
        /// Bölümün tüm performans kayıtlarını getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="limit">Maksimum kayıt sayısı</param>
        /// <returns>Performans entity listesi</returns>
        Task<List<DepartmentPerformanceEntity>> GetByDepartmentAsync(
            string departmentId,
            int? limit = null);

        /// <summary>
        /// Fakültenin tüm bölüm performanslarını getir
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si</param>
        /// <param name="period">Dönem (opsiyonel)</param>
        /// <returns>Performans entity listesi</returns>
        Task<List<DepartmentPerformanceEntity>> GetByFacultyAsync(
            string facultyId,
            string? period = null);

        /// <summary>
        /// Belirli dönemdeki tüm bölüm performanslarını getir
        /// </summary>
        /// <param name="period">Dönem</param>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel filtreleme)</param>
        /// <returns>Performans entity listesi</returns>
        Task<List<DepartmentPerformanceEntity>> GetByPeriodAsync(
            string period,
            string? facultyId = null);

        /// <summary>
        /// Bölümün son N dönemlik performanslarını getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="periodCount">Dönem sayısı</param>
        /// <returns>Performans entity listesi (tarih sıralı)</returns>
        Task<List<DepartmentPerformanceEntity>> GetRecentPerformancesAsync(
            string departmentId,
            int periodCount);

        #endregion

        #region Statistical Operations

        /// <summary>
        /// Bölüm performans istatistiklerini hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="periodCount">Dönem sayısı</param>
        /// <returns>İstatistik verileri</returns>
        Task<DepartmentStatisticsDto> CalculateStatisticsAsync(
            string departmentId,
            int periodCount);

        /// <summary>
        /// Fakülte geneli performans ortalamasını hesapla
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Ortalama performans değerleri</returns>
        Task<Dictionary<string, double>> CalculateFacultyAveragesAsync(
            string facultyId,
            string period);

        /// <summary>
        /// Genel performans benchmark'larını hesapla
        /// </summary>
        /// <param name="period">Dönem</param>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <returns>Benchmark değerleri</returns>
        Task<DepartmentBenchmarkDto> CalculateBenchmarkAsync(
            string period,
            string? facultyId = null);

        /// <summary>
        /// Bölüm sıralamasını hesapla
        /// </summary>
        /// <param name="period">Dönem</param>
        /// <param name="metric">Sıralama metriği</param>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <returns>Sıralanmış bölüm listesi</returns>
        Task<List<DepartmentRankingItemDto>> CalculateRankingAsync(
            string period,
            string metric = "OverallScore",
            string? facultyId = null);

        #endregion

        #region Trend Analysis

        /// <summary>
        /// Bölüm trend verilerini hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="periodCount">Dönem sayısı</param>
        /// <param name="metrics">Analiz edilecek metrikler</param>
        /// <returns>Trend verileri</returns>
        Task<List<DepartmentTrendDataDto>> CalculateTrendDataAsync(
            string departmentId,
            int periodCount,
            List<string> metrics);

        /// <summary>
        /// Bölüm büyüme oranını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="currentPeriod">Mevcut dönem</param>
        /// <param name="previousPeriod">Önceki dönem</param>
        /// <returns>Büyüme oranı</returns>
        Task<double> CalculateGrowthRateAsync(
            string departmentId,
            string currentPeriod,
            string previousPeriod);

        /// <summary>
        /// Çoklu bölüm karşılaştırma verilerini getir
        /// </summary>
        /// <param name="departmentIds">Bölüm ID'leri</param>
        /// <param name="period">Dönem</param>
        /// <param name="metrics">Karşılaştırılacak metrikler</param>
        /// <returns>Karşılaştırma verileri</returns>
        Task<Dictionary<string, Dictionary<string, double>>> GetComparisonDataAsync(
            List<string> departmentIds,
            string period,
            List<string> metrics);

        #endregion

        #region Aggregation Operations

        /// <summary>
        /// Bölüm performans verilerini topla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startPeriod">Başlangıç dönemi</param>
        /// <param name="endPeriod">Bitiş dönemi</param>
        /// <returns>Toplam performans verileri</returns>
        Task<DepartmentPerformanceAggregateDto> AggregatePerformanceDataAsync(
            string departmentId,
            string startPeriod,
            string endPeriod);

        /// <summary>
        /// Fakülte geneli performans toplamını hesapla
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Fakülte toplam performansı</returns>
        Task<FacultyPerformanceAggregateDto> AggregateFacultyPerformanceAsync(
            string facultyId,
            string period);

        #endregion

        #region Search Operations

        /// <summary>
        /// Bölüm performanslarında arama yap
        /// </summary>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="co">Sayfalama parametreleri</param>
        /// <returns>Arama sonuçları</returns>
        Task<PagedListDto<DepartmentPerformanceEntity>> SearchAsync(
            string searchTerm,
            PagedListCo<DepartmentPerformanceFilterCo> co);

        /// <summary>
        /// Gelişmiş filtreleme ile bölüm performanslarını getir
        /// </summary>
        /// <param name="filters">Filtre parametreleri</param>
        /// <returns>Filtrelenmiş sonuçlar</returns>
        Task<List<DepartmentPerformanceEntity>> GetWithAdvancedFiltersAsync(
            Dictionary<string, object> filters);

        #endregion

        #region Validation Operations

        /// <summary>
        /// Bölüm performans verilerinin tutarlılığını kontrol et
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Tutarlılık raporu</returns>
        Task<DataConsistencyReportDto> ValidateDataConsistencyAsync(
            string departmentId,
            string period);

        /// <summary>
        /// Duplicate kayıtları kontrol et
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Duplicate kayıt var mı</returns>
        Task<bool> CheckDuplicateRecordAsync(string departmentId, string period);

        #endregion

        #region Utility Operations

        /// <summary>
        /// Bölüm performans cache'ini temizle
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <returns>Temizleme başarılı mı</returns>
        Task<bool> ClearCacheAsync(string departmentId);

        /// <summary>
        /// Performans verilerini bulk insert et - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkInsertAsync(List<DepartmentPerformanceEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performans verilerini bulk update et - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkUpdateAsync(List<DepartmentPerformanceEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performans verilerini bulk delete et - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkDeleteAsync(List<DepartmentPerformanceEntity> entities, CancellationToken cancellationToken = default);

        #endregion
    }


}
