using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Controller'ların submission review, approval/rejection işlemleri için gerekli operasyonları sağlar
    /// </summary>
    public interface IControllerManager
    {
        #region Dashboard Operations

        /// <summary>
        /// Controller dashboard verilerini getir
        /// Pending submissions, statistics ve recent activities dahil
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Controller dashboard verileri</returns>
        Task<ControllerDashboardDto> GetControllerDashboardAsync(string controllerId);

        /// <summary>
        /// Controller'a atanmış pending submission'ları sayfalanmış olarak getir
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış pending submission listesi</returns>
        Task<PagedListDto<PendingSubmissionDto>> GetPendingSubmissionsAsync(string controllerId, PagedListCo<SubmissionFilterCo> co);

        /// <summary>
        /// Controller istatistiklerini getir
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Controller statistics</returns>
        Task<ControllerStatisticsDto> GetControllerStatisticsAsync(string controllerId);

        #endregion

        #region Submission Review Operations

        /// <summary>
        /// Submission'ı review için detaylı olarak getir
        /// Academician bilgileri, evidence files ve criterion data dahil
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si (authorization için)</param>
        /// <returns>Detaylı submission review verileri</returns>
        Task<SubmissionReviewDto> GetSubmissionForReviewAsync(string submissionId, string controllerId);

        /// <summary>
        /// Submission'a ait evidence file'ları getir
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si (authorization için)</param>
        /// <returns>Evidence file listesi</returns>
        Task<List<EvidenceFileDto>> GetSubmissionEvidenceFilesAsync(string submissionId, string controllerId);

        /// <summary>
        /// Submission'ın review edilebilir olup olmadığını kontrol et
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Review edilebilir mi?</returns>
        Task<bool> CanReviewSubmissionAsync(string submissionId, string controllerId);

        #endregion

        #region Approval/Rejection Operations

        /// <summary>
        /// Submission'ı onayla
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="comments">Onay yorumları (opsiyonel)</param>
        /// <returns>Onay işlemi başarılı mı?</returns>
        Task<bool> ApproveSubmissionAsync(string submissionId, string controllerId, string? comments = null);

        /// <summary>
        /// Submission'ı reddet
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="comments">Red yorumları (zorunlu)</param>
        /// <returns>Red işlemi başarılı mı?</returns>
        Task<bool> RejectSubmissionAsync(string submissionId, string controllerId, string comments);

        /// <summary>
        /// Submission'ın approve edilebilir olup olmadığını kontrol et
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Approve edilebilir mi?</returns>
        Task<bool> CanApproveSubmissionAsync(string submissionId, string controllerId);

        /// <summary>
        /// Submission'ın reject edilebilir olup olmadığını kontrol et
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Reject edilebilir mi?</returns>
        Task<bool> CanRejectSubmissionAsync(string submissionId, string controllerId);

        #endregion

        #region Assignment and Authorization

        /// <summary>
        /// Controller'ın belirli bir submission'a erişim yetkisi var mı kontrol et
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Erişim yetkisi var mı?</returns>
        Task<bool> HasAccessToSubmissionAsync(string controllerId, string submissionId);

        /// <summary>
        /// Submission'ı controller'a ata
        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="assignedByUserId">Atama yapan kullanıcı ID'si</param>
        /// <returns>Atama başarılı mı?</returns>
        Task<bool> AssignSubmissionToControllerAsync(string submissionId, string controllerId, string assignedByUserId);

        #endregion

        #region Helper Methods

        /// <summary>
        /// Submission status geçişinin geçerli olup olmadığını kontrol et
        /// </summary>
        /// <param name="currentStatus">Mevcut status</param>
        /// <param name="newStatus">Yeni status</param>
        /// <returns>Geçiş geçerli mi?</returns>
        Task<bool> IsValidStatusTransitionAsync(string currentStatus, string newStatus);

        /// <summary>
        /// Controller'ın günlük review limitini kontrol et
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <returns>Günlük limit aşılmış mı?</returns>
        Task<bool> HasReachedDailyReviewLimitAsync(string controllerId);

        #endregion
    }
}
