using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    public interface ICriteriaStore
    {
        // Dynamic Criteria (MongoDB)
        Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync();
        Task<DynamicCriterionTemplate?> GetDynamicCriterionTemplateByIdAsync(string id);
        Task<List<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesByStatusAsync(string status);
        Task<DynamicCriterionTemplate> CreateDynamicCriterionTemplateAsync(DynamicCriterionTemplate template);
        Task<bool> UpdateDynamicCriterionTemplateAsync(string id, DynamicCriterionTemplate template);
        Task<bool> DeleteDynamicCriterionTemplateAsync(string id);
        Task<bool> UpdateDynamicCriterionTemplateStatusAsync(string id, string status);

        // Dynamic Criteria - Pagination
        Task<PagedListDto<DynamicCriterionTemplate>> GetDynamicCriterionTemplatesAsync(PagedListCo<GetDynamicCriterionTemplatesCo> co);

        // Static Criteria (PostgreSQL)
        Task<List<StaticCriterionDefinitionEntity>> GetStaticCriterionDefinitionsAsync();
        Task<StaticCriterionDefinitionEntity?> GetStaticCriterionDefinitionByIdAsync(string staticCriterionSystemId);
        Task<List<StaticCriterionDefinitionEntity>> GetActiveStaticCriterionDefinitionsAsync();
        Task<bool> UpdateStaticCriterionDefinitionStatusAsync(string staticCriterionSystemId, bool isActive);
        Task<StaticCriterionDefinitionEntity> CreateStaticCriterionDefinitionAsync(StaticCriterionDefinitionEntity entity, bool saveChanges = true);
        Task<bool> UpdateStaticCriterionDefinitionAsync(StaticCriterionDefinitionEntity entity, bool saveChanges = true);

        // Static Criteria - Pagination
        Task<PagedListDto<StaticCriterionDefinitionEntity>> GetStaticCriterionDefinitionsAsync(PagedListCo<GetStaticCriterionDefinitionsCo> co);
    }
}
