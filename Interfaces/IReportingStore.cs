using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Reporting veritabanı işlemleri interface'i
    /// </summary>
    public interface IReportingStore
    {
        #region Performance Data Retrieval

        /// <summary>
        /// Akademisyen performans verilerini getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Akademisyen performans verileri</returns>
        Task<AcademicianPerformanceDataDto> GetAcademicianPerformanceDataAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Çoklu akademisyen performans verilerini getir (sayfalanmış)
        /// </summary>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış akademisyen performans verileri</returns>
        Task<PagedListDto<AcademicianPerformanceDataDto>> GetMultipleAcademicianPerformanceDataAsync(PagedListCo<PerformanceReportFilterCo> co);

        /// <summary>
        /// Bölüm performans verilerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm performans verileri</returns>
        Task<DepartmentPerformanceDataDto> GetDepartmentPerformanceDataAsync(string departmentId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Çoklu bölüm performans verilerini getir
        /// </summary>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Sayfalanmış bölüm performans verileri</returns>
        Task<PagedListDto<DepartmentPerformanceDataDto>> GetMultipleDepartmentPerformanceDataAsync(PagedListCo<DepartmentReportFilterCo> co);

        #endregion

        #region Aggregation and Statistics

        /// <summary>
        /// Akademisyen kategori skorlarını hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Kategori skorları</returns>
        Task<List<CategoryPerformanceDto>> CalculateAcademicianCategoryScoresAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Akademisyen kriter skorlarını hesapla
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Kriter skorları</returns>
        Task<List<CriterionPerformanceDto>> CalculateAcademicianCriterionScoresAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Bölüm kategori ortalamalarını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Kategori ortalamaları</returns>
        Task<List<CategoryPerformanceDto>> CalculateDepartmentCategoryAveragesAsync(string departmentId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Performans dağılımını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Performans dağılımı</returns>
        Task<PerformanceDistributionDto> CalculatePerformanceDistributionAsync(string departmentId, DateTime startDate, DateTime endDate);

        #endregion

        #region Criterion Analysis

        /// <summary>
        /// Kriter analiz verilerini getir
        /// </summary>
        /// <param name="criterionId">Kriter ID'si</param>
        /// <param name="filterCo">Filtreleme kriterleri</param>
        /// <returns>Kriter analiz verileri</returns>
        Task<CriterionAnalysisDataDto> GetCriterionAnalysisDataAsync(string criterionId, CriterionAnalysisFilterCo filterCo);

        /// <summary>
        /// Kriter istatistiklerini hesapla
        /// </summary>
        /// <param name="criterionId">Kriter ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Kriter istatistikleri</returns>
        Task<CriterionStatisticsDto> CalculateCriterionStatisticsAsync(string criterionId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Bölüm bazında kriter performansını getir
        /// </summary>
        /// <param name="criterionId">Kriter ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm kriter performansları</returns>
        Task<List<DepartmentCriterionAnalysisDto>> GetDepartmentCriterionPerformanceAsync(string criterionId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Akademisyen bazında kriter performansını getir
        /// </summary>
        /// <param name="criterionId">Kriter ID'si</param>
        /// <param name="co">Sayfalama ve filtreleme kriterleri</param>
        /// <returns>Akademisyen kriter performansları</returns>
        Task<PagedListDto<AcademicianCriterionPerformanceDto>> GetAcademicianCriterionPerformanceAsync(string criterionId, PagedListCo<CriterionAnalysisFilterCo> co);

        #endregion

        #region Trend Analysis

        /// <summary>
        /// Trend verilerini getir
        /// </summary>
        /// <param name="filterCo">Trend analizi kriterleri</param>
        /// <returns>Trend verileri</returns>
        Task<List<TrendDataPointDto>> GetTrendDataAsync(TrendAnalysisFilterCo filterCo);

        /// <summary>
        /// Akademisyen trend verilerini getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="filterCo">Trend analizi kriterleri</param>
        /// <returns>Akademisyen trend verileri</returns>
        Task<List<TrendDataPointDto>> GetAcademicianTrendDataAsync(string academicianUserId, TrendAnalysisFilterCo filterCo);

        /// <summary>
        /// Bölüm trend verilerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="filterCo">Trend analizi kriterleri</param>
        /// <returns>Bölüm trend verileri</returns>
        Task<List<TrendDataPointDto>> GetDepartmentTrendDataAsync(string departmentId, TrendAnalysisFilterCo filterCo);

        /// <summary>
        /// Aylık trend verilerini getir
        /// </summary>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <returns>Aylık trend verileri</returns>
        Task<List<MonthlyTrendDto>> GetMonthlyTrendDataAsync(DateTime startDate, DateTime endDate, string? departmentId = null);

        #endregion

        #region Ranking and Comparison

        /// <summary>
        /// Akademisyen sıralamasını getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si (opsiyonel)</param>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <param name="limit">Limit (opsiyonel)</param>
        /// <returns>Sıralanmış akademisyen listesi</returns>
        Task<List<AcademicianPerformanceSummaryDto>> GetAcademicianRankingAsync(string? departmentId, string? facultyId, DateTime startDate, DateTime endDate, int? limit = null);

        /// <summary>
        /// Bölüm sıralamasını getir
        /// </summary>
        /// <param name="facultyId">Fakülte ID'si (opsiyonel)</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Sıralanmış bölüm listesi</returns>
        Task<List<DepartmentSummaryDto>> GetDepartmentRankingAsync(string? facultyId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Karşılaştırmalı veriler getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Karşılaştırmalı veriler</returns>
        Task<ComparativeDataDto> GetComparativeDataAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        #endregion

        #region Form and Submission Data

        /// <summary>
        /// Form tamamlanma istatistiklerini getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Form tamamlanma istatistikleri</returns>
        Task<FormCompletionStatsDto> GetFormCompletionStatsAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Bölüm form istatistiklerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm form istatistikleri</returns>
        Task<FormCompletionStatsDto> GetDepartmentFormStatsAsync(string departmentId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Form bazında performans detaylarını getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Form performans detayları</returns>
        Task<List<FormPerformanceDetailDto>> GetFormPerformanceDetailsAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        #endregion

        #region Feedback Data

        /// <summary>
        /// Feedback istatistiklerini getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Feedback istatistikleri</returns>
        Task<FeedbackStatisticsDto> GetFeedbackStatisticsAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Bölüm feedback istatistiklerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Bölüm feedback istatistikleri</returns>
        Task<DepartmentFeedbackStatsDto> GetDepartmentFeedbackStatsAsync(string departmentId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Feedback detaylarını getir
        /// </summary>
        /// <param name="academicianUserId">Akademisyen kullanıcı ID'si</param>
        /// <param name="co">Sayfalama kriterleri</param>
        /// <returns>Feedback detayları</returns>
        Task<PagedListDto<FeedbackDetailDto>> GetFeedbackDetailsAsync(string academicianUserId, PagedListCo<object> co);

        #endregion

        #region Report Storage

        /// <summary>
        /// Raporu kaydet
        /// </summary>
        /// <param name="reportData">Rapor verisi</param>
        /// <param name="reportType">Rapor türü</param>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <returns>Rapor ID'si</returns>
        Task<string> SaveReportAsync(object reportData, string reportType, string userId);

        /// <summary>
        /// Kaydedilmiş raporu getir
        /// </summary>
        /// <param name="reportId">Rapor ID'si</param>
        /// <returns>Rapor verisi</returns>
        Task<object?> GetSavedReportAsync(string reportId);

        /// <summary>
        /// Kaydedilmiş raporları listele
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="co">Sayfalama kriterleri</param>
        /// <returns>Sayfalanmış rapor listesi</returns>
        Task<PagedListDto<object>> GetSavedReportsAsync(string userId, PagedListCo<object> co);

        /// <summary>
        /// Raporu sil
        /// </summary>
        /// <param name="reportId">Rapor ID'si</param>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <returns>Silme başarılı mı</returns>
        Task<bool> DeleteSavedReportAsync(string reportId, string userId);

        #endregion
    }


}
