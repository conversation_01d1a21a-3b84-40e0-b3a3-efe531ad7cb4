using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;

namespace AcademicPerformance.Interfaces
{
    public interface IAcademicianStore
    {
        // Academician Profile CRUD Operations
        Task<List<AcademicianProfileEntity>> GetAcademicianProfilesAsync();
        Task<AcademicianProfileEntity?> GetAcademicianProfileByIdAsync(string id);
        Task<AcademicianProfileEntity?> GetAcademicianProfileByUniversityUserIdAsync(string universityUserId);
        Task<AcademicianProfileEntity?> GetAcademicianProfileForUpdateAsync(string id);
        Task<AcademicianProfileEntity> CreateAcademicianProfileAsync(AcademicianProfileEntity entity, bool saveChanges = true);
        Task<bool> UpdateAcademicianProfileAsync(AcademicianProfileEntity entity, bool saveChanges = true);
        Task<bool> DeleteAcademicianProfileAsync(string id);
        Task<bool> SoftDeleteAcademicianProfileAsync(string id);

        // Filtering and Search Operations
        Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByDepartmentAsync(string department);
        Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByAcademicCadreAsync(string academicCadre);
        Task<List<AcademicianProfileEntity>> GetActiveAcademicianProfilesAsync();
        Task<List<AcademicianProfileEntity>> GetAcademicianProfilesRequiringSyncAsync(DateTime? olderThan = null);
        Task<List<AcademicianProfileEntity>> SearchAcademicianProfilesAsync(string searchTerm);

        // Bulk Operations - Enhanced with BulkOperationExtensions
        Task<BulkOperationResult<List<AcademicianProfileEntity>>> CreateAcademicianProfilesAsync(List<AcademicianProfileEntity> entities, CancellationToken cancellationToken = default);
        Task<BulkOperationResult> UpdateAcademicianProfilesAsync(List<AcademicianProfileEntity> entities, CancellationToken cancellationToken = default);
        Task<List<AcademicianProfileEntity>> GetAcademicianProfilesByUniversityUserIdsAsync(List<string> universityUserIds);

        // Sync Operations - Enhanced with BulkOperationExtensions
        Task<bool> UpdateLastSyncedAtAsync(string universityUserId, DateTime syncedAt, string? syncNotes = null);
        Task<BulkOperationResult> UpdateLastSyncedAtBulkAsync(List<string> universityUserIds, DateTime syncedAt, string? syncNotes = null, CancellationToken cancellationToken = default);

        // Dashboard Related Operations
        Task<List<AcademicianProfileEntity>> GetAcademicianProfilesWithSubmissionsAsync();
        Task<AcademicianProfileEntity?> GetAcademicianProfileWithSubmissionsAsync(string universityUserId);

        // Statistics and Analytics
        Task<int> GetTotalAcademicianCountAsync();
        Task<int> GetActiveAcademicianCountAsync();
        Task<Dictionary<string, int>> GetAcademicianCountByDepartmentAsync();
        Task<Dictionary<string, int>> GetAcademicianCountByAcademicCadreAsync();
        Task<int> GetAcademicianCountRequiringSyncAsync(DateTime? olderThan = null);

        // Helper Methods
        Task<bool> AcademicianProfileExistsAsync(string id);
        Task<bool> AcademicianProfileExistsByUniversityUserIdAsync(string universityUserId);
        Task<IDictionary<string, int>> IdConvertForAcademicianProfile(IEnumerable<string> profileIds);
        Task<IDictionary<string, string>> UniversityUserIdToProfileIdConvertAsync(IEnumerable<string> universityUserIds);

        // Validation Methods
        Task<bool> ValidateUniversityUserIdUniqueAsync(string universityUserId, string? excludeProfileId = null);
        Task<List<string>> GetDuplicateUniversityUserIdsAsync(List<string> universityUserIds);
    }
}
