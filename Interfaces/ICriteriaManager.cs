using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    public interface ICriteriaManager
    {
        // Dynamic Criteria Management
        Task<List<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesAsync();
        Task<DynamicCriterionTemplateDto?> GetDynamicCriterionTemplateByIdAsync(string id);
        Task<List<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesByStatusAsync(string status);
        Task<List<DynamicCriterionTemplateDto>> GetActiveDynamicCriterionTemplatesAsync();
        Task<DynamicCriterionTemplateDto> CreateDynamicCriterionTemplateAsync(DynamicCriterionTemplateCreateDto dto, string createdByUserId);
        Task<bool> UpdateDynamicCriterionTemplateAsync(DynamicCriterionTemplateUpdateDto dto, string updatedByUserId);
        Task<bool> UpdateDynamicCriterionTemplateStatusAsync(string id, string status, string updatedByUserId);
        Task<bool> DeleteDynamicCriterionTemplateAsync(string id);

        // Dynamic Criteria Management - Pagination
        Task<PagedListDto<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesAsync(PagedListCo<GetDynamicCriterionTemplatesCo> co);

        // Static Criteria Management
        Task<List<StaticCriterionDefinitionDto>> GetStaticCriterionDefinitionsAsync(string? culture = null);
        Task<StaticCriterionDefinitionDto?> GetStaticCriterionDefinitionByIdAsync(string staticCriterionSystemId);
        Task<List<StaticCriterionDefinitionDto>> GetActiveStaticCriterionDefinitionsAsync();
        Task<bool> UpdateStaticCriterionDefinitionStatusAsync(StaticCriterionDefinitionUpdateDto dto);

        // Static Criteria Management - Pagination
        Task<PagedListDto<StaticCriterionDefinitionDto>> GetStaticCriterionDefinitionsAsync(PagedListCo<GetStaticCriterionDefinitionsCo> co, string? culture = null);

        // Status-based Pagination Methods
        Task<PagedListDto<DynamicCriterionTemplateDto>> GetDynamicCriterionTemplatesByStatusAsync(string status, PagedListCo<GetStatusFilterCo> co);
        Task<PagedListDto<DynamicCriterionTemplateDto>> GetActiveDynamicCriterionTemplatesAsync(PagedListCo<GetStatusFilterCo> co);
        Task<PagedListDto<StaticCriterionDefinitionDto>> GetActiveStaticCriterionDefinitionsAsync(PagedListCo<GetStatusFilterCo> co);

        // Validation
        Task<bool> ValidateDynamicCriterionTemplateAsync(DynamicCriterionTemplateCreateDto dto);
        Task<bool> CanUpdateDynamicCriterionTemplateAsync(string id);
        Task<bool> CanDeleteDynamicCriterionTemplateAsync(string id);
    }
}
