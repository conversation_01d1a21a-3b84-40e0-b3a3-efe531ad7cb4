using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Generic Data Entry Manager interface
    /// </summary>
    public interface IGenericDataEntryManager
    {
        #region Definition Management

        /// <summary>
        /// Generic data entry definition oluştur
        /// </summary>
        /// <param name="dto">Definition DTO</param>
        /// <param name="createdByUserId">Oluşturan kullanıcı ID</param>
        /// <returns>Oluşturulan definition</returns>
        Task<GenericDataEntryDefinitionDto> CreateDefinitionAsync(GenericDataEntryDefinitionCreateDto dto, string createdByUserId);

        /// <summary>
        /// Generic data entry definition güncelle
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <param name="dto">Güncelleme DTO</param>
        /// <param name="updatedByUserId">Güncelleyen kullanıcı ID</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateDefinitionAsync(string definitionId, GenericDataEntryDefinitionUpdateDto dto, string updatedByUserId);

        /// <summary>
        /// Generic data entry definition sil
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <param name="deletedByUserId">Silen kullanıcı ID</param>
        /// <returns>Silme başarılı mı?</returns>
        Task<bool> DeleteDefinitionAsync(string definitionId, string deletedByUserId);

        /// <summary>
        /// Generic data entry definition getir
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Definition DTO</returns>
        Task<GenericDataEntryDefinitionDto?> GetDefinitionByIdAsync(string definitionId);

        /// <summary>
        /// Generic data entry definition'ları listele
        /// </summary>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış definition listesi</returns>
        Task<PagedListDto<GenericDataEntryDefinitionDto>> GetDefinitionsAsync(PagedListCo<GenericDataEntryFilterCo> co);

        #endregion

        #region Record Management

        /// <summary>
        /// Generic data entry record oluştur
        /// </summary>
        /// <param name="dto">Record DTO</param>
        /// <param name="createdByUserId">Oluşturan kullanıcı ID</param>
        /// <returns>Oluşturulan record</returns>
        Task<GenericDataEntryRecordDto> CreateRecordAsync(GenericDataEntryRecordCreateDto dto, string createdByUserId);

        /// <summary>
        /// Generic data entry record güncelle
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <param name="dto">Güncelleme DTO</param>
        /// <param name="updatedByUserId">Güncelleyen kullanıcı ID</param>
        /// <returns>Güncelleme başarılı mı?</returns>
        Task<bool> UpdateRecordAsync(string recordId, GenericDataEntryRecordUpdateDto dto, string updatedByUserId);

        /// <summary>
        /// Generic data entry record sil
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <param name="deletedByUserId">Silen kullanıcı ID</param>
        /// <returns>Silme başarılı mı?</returns>
        Task<bool> DeleteRecordAsync(string recordId, string deletedByUserId);

        /// <summary>
        /// Generic data entry record getir
        /// </summary>
        /// <param name="recordId">Record ID</param>
        /// <returns>Record DTO</returns>
        Task<GenericDataEntryRecordDto?> GetRecordByIdAsync(string recordId);

        /// <summary>
        /// Generic data entry record'ları listele
        /// </summary>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış record listesi</returns>
        Task<PagedListDto<GenericDataEntryRecordDto>> GetRecordsAsync(PagedListCo<GenericDataEntryFilterCo> co);

        /// <summary>
        /// Belirli definition için record'ları getir
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <param name="co">Pagination ve filtreleme</param>
        /// <returns>Sayfalanmış record listesi</returns>
        Task<PagedListDto<GenericDataEntryRecordDto>> GetRecordsByDefinitionAsync(string definitionId, PagedListCo<GenericDataEntryFilterCo> co);

        #endregion

        #region Validation and Business Logic

        /// <summary>
        /// Definition validation
        /// </summary>
        /// <param name="dto">Definition DTO</param>
        /// <returns>Validation sonucu</returns>
        Task<ValidationResultDto> ValidateDefinitionAsync(GenericDataEntryDefinitionCreateDto dto);

        /// <summary>
        /// Record validation
        /// </summary>
        /// <param name="dto">Record DTO</param>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Validation sonucu</returns>
        Task<ValidationResultDto> ValidateRecordAsync(GenericDataEntryRecordCreateDto dto, string definitionId);

        /// <summary>
        /// Definition kullanımda mı kontrol et
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>Kullanımda mı?</returns>
        Task<bool> IsDefinitionInUseAsync(string definitionId);

        #endregion

        #region Statistics and Analytics

        /// <summary>
        /// Definition istatistikleri
        /// </summary>
        /// <param name="definitionId">Definition ID</param>
        /// <returns>İstatistik DTO</returns>
        Task<GenericDataEntryStatisticsDto> GetDefinitionStatisticsAsync(string definitionId);

        /// <summary>
        /// Genel istatistikler
        /// </summary>
        /// <returns>Genel istatistikler</returns>
        Task<GenericDataEntryOverallStatisticsDto> GetOverallStatisticsAsync();

        #endregion
    }
}
