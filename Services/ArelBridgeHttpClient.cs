using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.Text;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// ArelBridge API'sine HTTP istekleri yapmak için client servis implementasyonu
    /// Akademik eğitim faaliyetleri statik kriterlerini ArelBridge'den çekmek için kullanılır
    /// </summary>
    public class ArelBridgeHttpClient : IArelBridgeHttpClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ArelBridgeHttpClient> _logger;
        private readonly ArelBridgeOptions _options;
        private readonly JsonSerializerOptions _jsonOptions;

        public ArelBridgeHttpClient(
            HttpClient httpClient,
            ILogger<ArelBridgeHttpClient> logger,
            IOptions<ArelBridgeOptions> options)
        {
            _httpClient = httpClient;
            _logger = logger;
            _options = options.Value;

            // JSON serialization options
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };

            // Configure HttpClient
            _httpClient.BaseAddress = new Uri(_options.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);

            // Add default headers
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "AcademicPerformance/1.0");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

            // Add authorization header if API key is provided
            if (!string.IsNullOrEmpty(_options.ApiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_options.ApiKey}");
            }
        }

        /// <summary>
        /// Belirli bir akademisyen için akademik eğitim faaliyetleri verilerini ArelBridge'den getirir
        /// </summary>
        public async Task<ArelBridgeAcademicEducationActivityResponseDto?> GetAcademicEducationActivityAsync(string academicianTc)
        {
            try
            {
                _logger.LogInformation("Getting academic education activity from ArelBridge for academician: {AcademicianTc}", academicianTc);

                var response = await _httpClient.GetAsync($"api/v1/AcademicEducationActivity/{academicianTc}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ArelBridgeAcademicEducationActivityResponseDto>(content, _jsonOptions);
                    
                    _logger.LogInformation("Successfully retrieved academic education activity from ArelBridge for academician: {AcademicianTc}", academicianTc);
                    return result;
                }
                else
                {
                    _logger.LogWarning("ArelBridge API returned error status: {StatusCode} for academician: {AcademicianTc}", 
                        response.StatusCode, academicianTc);
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request error when calling ArelBridge API for academician: {AcademicianTc}", academicianTc);
                return null;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "Timeout error when calling ArelBridge API for academician: {AcademicianTc}", academicianTc);
                return null;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "JSON deserialization error when calling ArelBridge API for academician: {AcademicianTc}", academicianTc);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error when calling ArelBridge API for academician: {AcademicianTc}", academicianTc);
                return null;
            }
        }

        /// <summary>
        /// Detaylı parametrelerle akademik eğitim faaliyetleri verilerini ArelBridge'den getirir
        /// </summary>
        public async Task<ArelBridgeAcademicEducationActivityResponseDto?> GetAcademicEducationActivityDetailedAsync(ArelBridgeAcademicEducationActivityRequestDto request)
        {
            try
            {
                _logger.LogInformation("Getting detailed academic education activity from ArelBridge for academician: {AcademicianTc}", request.AcademicianTc);

                var json = JsonSerializer.Serialize(request, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("api/v1/AcademicEducationActivity/detailed", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ArelBridgeAcademicEducationActivityResponseDto>(responseContent, _jsonOptions);
                    
                    _logger.LogInformation("Successfully retrieved detailed academic education activity from ArelBridge for academician: {AcademicianTc}", request.AcademicianTc);
                    return result;
                }
                else
                {
                    _logger.LogWarning("ArelBridge API returned error status: {StatusCode} for detailed request for academician: {AcademicianTc}", 
                        response.StatusCode, request.AcademicianTc);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when calling ArelBridge API for detailed request for academician: {AcademicianTc}", request.AcademicianTc);
                return null;
            }
        }

        /// <summary>
        /// Belirli kriterlerin verilerini ArelBridge'den getirir
        /// </summary>
        public async Task<ArelBridgeAcademicEducationActivityResponseDto?> GetSpecificCriteriaAsync(string academicianTc, List<string> criterionIds)
        {
            try
            {
                _logger.LogInformation("Getting specific criteria from ArelBridge for academician: {AcademicianTc}, Criteria: {CriterionIds}", 
                    academicianTc, string.Join(", ", criterionIds));

                var queryString = string.Join("&", criterionIds.Select(id => $"criterionIds={id}"));
                var response = await _httpClient.GetAsync($"api/v1/AcademicEducationActivity/{academicianTc}/criteria?{queryString}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ArelBridgeAcademicEducationActivityResponseDto>(content, _jsonOptions);
                    
                    _logger.LogInformation("Successfully retrieved specific criteria from ArelBridge for academician: {AcademicianTc}", academicianTc);
                    return result;
                }
                else
                {
                    _logger.LogWarning("ArelBridge API returned error status: {StatusCode} for specific criteria for academician: {AcademicianTc}", 
                        response.StatusCode, academicianTc);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when calling ArelBridge API for specific criteria for academician: {AcademicianTc}", academicianTc);
                return null;
            }
        }

        /// <summary>
        /// AcademicPerformance formatına dönüştürülmüş veriyi ArelBridge'den getirir
        /// </summary>
        public async Task<Dictionary<string, object>?> GetAcademicPerformanceFormatAsync(string academicianTc)
        {
            try
            {
                _logger.LogInformation("Getting academic performance format from ArelBridge for academician: {AcademicianTc}", academicianTc);

                var response = await _httpClient.GetAsync($"api/v1/AcademicEducationActivity/{academicianTc}/academic-performance-format");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<Dictionary<string, object>>(content, _jsonOptions);
                    
                    _logger.LogInformation("Successfully retrieved academic performance format from ArelBridge for academician: {AcademicianTc}", academicianTc);
                    return result;
                }
                else
                {
                    _logger.LogWarning("ArelBridge API returned error status: {StatusCode} for academic performance format for academician: {AcademicianTc}", 
                        response.StatusCode, academicianTc);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when calling ArelBridge API for academic performance format for academician: {AcademicianTc}", academicianTc);
                return null;
            }
        }

        /// <summary>
        /// Kriter eşleme konfigürasyonlarını ArelBridge'den getirir
        /// </summary>
        public async Task<List<ArelBridgeCriterionMappingDto>?> GetCriterionMappingsAsync()
        {
            try
            {
                _logger.LogInformation("Getting criterion mappings from ArelBridge");

                var response = await _httpClient.GetAsync("api/v1/AcademicEducationActivity/criterion-mappings");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<List<ArelBridgeCriterionMappingDto>>(content, _jsonOptions);
                    
                    _logger.LogInformation("Successfully retrieved criterion mappings from ArelBridge. Count: {Count}", result?.Count ?? 0);
                    return result;
                }
                else
                {
                    _logger.LogWarning("ArelBridge API returned error status: {StatusCode} for criterion mappings", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when calling ArelBridge API for criterion mappings");
                return null;
            }
        }

        /// <summary>
        /// ArelBridge servis bağlantısını test eder
        /// </summary>
        public async Task<(bool IsConnected, string Message)> TestConnectionAsync()
        {
            try
            {
                _logger.LogInformation("Testing ArelBridge connection");

                var response = await _httpClient.GetAsync("api/v1/AcademicEducationActivity/statistics");

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("ArelBridge connection test successful");
                    return (true, "ArelBridge bağlantısı başarılı");
                }
                else
                {
                    _logger.LogWarning("ArelBridge connection test failed with status: {StatusCode}", response.StatusCode);
                    return (false, $"ArelBridge bağlantı hatası: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ArelBridge connection test failed with exception");
                return (false, $"ArelBridge bağlantı hatası: {ex.Message}");
            }
        }

        /// <summary>
        /// Akademik eğitim faaliyetleri istatistiklerini ArelBridge'den getirir
        /// </summary>
        public async Task<ArelBridgeAcademicEducationActivityStatisticsDto?> GetStatisticsAsync(List<string>? academicianTcs = null)
        {
            try
            {
                _logger.LogInformation("Getting statistics from ArelBridge");

                var queryString = academicianTcs != null && academicianTcs.Any() 
                    ? "?" + string.Join("&", academicianTcs.Select(tc => $"academicianTcs={tc}"))
                    : "";

                var response = await _httpClient.GetAsync($"api/v1/AcademicEducationActivity/statistics{queryString}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ArelBridgeAcademicEducationActivityStatisticsDto>(content, _jsonOptions);
                    
                    _logger.LogInformation("Successfully retrieved statistics from ArelBridge");
                    return result;
                }
                else
                {
                    _logger.LogWarning("ArelBridge API returned error status: {StatusCode} for statistics", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when calling ArelBridge API for statistics");
                return null;
            }
        }

        /// <summary>
        /// Toplu akademisyen verilerini ArelBridge'den getirir
        /// </summary>
        public async Task<List<ArelBridgeAcademicEducationActivityResponseDto>> GetBatchAcademicEducationActivitiesAsync(List<string> academicianTcs)
        {
            var results = new List<ArelBridgeAcademicEducationActivityResponseDto>();

            try
            {
                _logger.LogInformation("Getting batch academic education activities from ArelBridge for {Count} academicians", academicianTcs.Count);

                // Process in batches to avoid overwhelming the API
                var batchSize = _options.BatchSize;
                for (int i = 0; i < academicianTcs.Count; i += batchSize)
                {
                    var batch = academicianTcs.Skip(i).Take(batchSize);
                    var batchTasks = batch.Select(tc => GetAcademicEducationActivityAsync(tc));
                    var batchResults = await Task.WhenAll(batchTasks);

                    foreach (var result in batchResults)
                    {
                        if (result != null)
                        {
                            results.Add(result);
                        }
                    }

                    // Add delay between batches to be respectful to the API
                    if (i + batchSize < academicianTcs.Count)
                    {
                        await Task.Delay(_options.BatchDelayMs);
                    }
                }

                _logger.LogInformation("Successfully retrieved {Count} academic education activities from ArelBridge", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when calling ArelBridge API for batch academic education activities");
                return results;
            }
        }

        /// <summary>
        /// ArelBridge API'nin sağlık durumunu kontrol eder
        /// </summary>
        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                var (isConnected, _) = await TestConnectionAsync();
                return isConnected;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// ArelBridge konfigürasyon seçenekleri
    /// </summary>
    public class ArelBridgeOptions
    {
        public const string SectionName = "ArelBridge";

        /// <summary>
        /// ArelBridge API base URL'i
        /// </summary>
        public required string BaseUrl { get; set; }

        /// <summary>
        /// API anahtarı (opsiyonel)
        /// </summary>
        public string? ApiKey { get; set; }

        /// <summary>
        /// HTTP timeout süresi (saniye)
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Batch işlem boyutu
        /// </summary>
        public int BatchSize { get; set; } = 10;

        /// <summary>
        /// Batch'ler arası bekleme süresi (ms)
        /// </summary>
        public int BatchDelayMs { get; set; } = 1000;

        /// <summary>
        /// Retry sayısı
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Retry bekleme süresi (ms)
        /// </summary>
        public int RetryDelayMs { get; set; } = 2000;
    }
}
