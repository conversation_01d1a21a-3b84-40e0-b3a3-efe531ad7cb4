using System.Security.Claims;
using AcademicPerformance.Consts;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Authorization helper service - hardcoded claim check'leri policy-based authorization ile değiştirmek için
    /// </summary>
    public interface IAPAuthorizationService
    {
        /// <summary>
        /// Kullanıcının admin yetkisine sahip olup olmadığını kontrol eder
        /// </summary>
        bool IsAdmin(ClaimsPrincipal user);

        /// <summary>
        /// Kullanıcının belirli bir permission'a sahip olup olmadığını kontrol eder
        /// </summary>
        bool HasPermission(ClaimsPrincipal user, string permissionType, string permissionValue);
    }

    /// <summary>
    /// Authorization helper service implementation
    /// Policy-based authorization logic'ini centralize eder
    /// </summary>
    public class APAuthorizationService : IAPAuthorizationService
    {
        /// <summary>
        /// Kullanıcının admin yetkisine sahip olup olmadığını kontrol eder
        /// Admin bypass logic: SuperAdmin role, Admin role veya "all" permission'ları
        /// </summary>
        public bool IsAdmin(ClaimsPrincipal user)
        {
            return user.HasClaim("role", "SuperAdmin") ||
                   user.HasClaim("role", "Admin") ||
                   user.HasClaim(APConsts.PermissionActionAll, "all") ||
                   user.HasClaim(APConsts.PermissionPageAll, "all") ||
                   user.HasClaim(APConsts.PermissionActionAP, "all") ||
                   user.HasClaim(APConsts.PermissionPageAP, "all");
        }

        /// <summary>
        /// Kullanıcının belirli bir permission'a sahip olup olmadığını kontrol eder
        /// </summary>
        public bool HasPermission(ClaimsPrincipal user, string permissionType, string permissionValue)
        {
            // Admin bypass kontrolü
            if (IsAdmin(user))
                return true;

            // Spesifik permission kontrolü
            return user.HasClaim(permissionType, permissionValue);
        }
    }
}
