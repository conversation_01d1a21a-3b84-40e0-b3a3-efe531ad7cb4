using AcademicPerformance.Models.Configurations;
using AcademicPerformance.Services.Interfaces;

namespace AcademicPerformance.Services.Implementations;

/// <summary>
/// Advanced file content validation service with magic number detection
/// </summary>
public class FileContentValidationService : IFileContentValidationService
{
    private readonly ILogger<FileContentValidationService> _logger;
    
    // Magic number signatures for common file types
    private static readonly Dictionary<string, List<byte[]>> MagicNumbers = new()
    {
        // PDF files
        [".pdf"] = new List<byte[]> { new byte[] { 0x25, 0x50, 0x44, 0x46 } }, // %PDF
        
        // Microsoft Office (newer formats)
        [".docx"] = new List<byte[]> { new byte[] { 0x50, 0x4B, 0x03, 0x04 } }, // PK..
        [".xlsx"] = new List<byte[]> { new byte[] { 0x50, 0x4B, 0x03, 0x04 } }, // PK..
        [".pptx"] = new List<byte[]> { new byte[] { 0x50, 0x4B, 0x03, 0x04 } }, // PK..
        
        // Microsoft Office (older formats)
        [".doc"] = new List<byte[]> { new byte[] { 0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1 } },
        [".xls"] = new List<byte[]> { new byte[] { 0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1 } },
        [".ppt"] = new List<byte[]> { new byte[] { 0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1 } },
        
        // Images
        [".jpg"] = new List<byte[]> { 
            new byte[] { 0xFF, 0xD8, 0xFF, 0xE0 }, // JFIF
            new byte[] { 0xFF, 0xD8, 0xFF, 0xE1 }, // EXIF
            new byte[] { 0xFF, 0xD8, 0xFF, 0xDB } // Raw JPEG
        },
        [".jpeg"] = new List<byte[]> { 
            new byte[] { 0xFF, 0xD8, 0xFF, 0xE0 },
            new byte[] { 0xFF, 0xD8, 0xFF, 0xE1 },
            new byte[] { 0xFF, 0xD8, 0xFF, 0xDB }
        },
        [".png"] = new List<byte[]> { new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A } },
        [".gif"] = new List<byte[]> { 
            new byte[] { 0x47, 0x49, 0x46, 0x38, 0x37, 0x61 }, // GIF87a
            new byte[] { 0x47, 0x49, 0x46, 0x38, 0x39, 0x61 }  // GIF89a
        },
        [".bmp"] = new List<byte[]> { new byte[] { 0x42, 0x4D } }, // BM
        
        // Archives
        [".zip"] = new List<byte[]> { 
            new byte[] { 0x50, 0x4B, 0x03, 0x04 }, // PK..
            new byte[] { 0x50, 0x4B, 0x05, 0x06 }, // Empty archive
            new byte[] { 0x50, 0x4B, 0x07, 0x08 }  // Spanned archive
        },
        [".rar"] = new List<byte[]> { 
            new byte[] { 0x52, 0x61, 0x72, 0x21, 0x1A, 0x07, 0x00 }, // Rar!...
            new byte[] { 0x52, 0x61, 0x72, 0x21, 0x1A, 0x07, 0x01, 0x00 } // RAR 5.0
        },
        [".7z"] = new List<byte[]> { new byte[] { 0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C } }, // 7z¼¯'..
        
        // Text files
        [".txt"] = new List<byte[]> { }, // Text files can start with anything, skip magic number check
        [".rtf"] = new List<byte[]> { new byte[] { 0x7B, 0x5C, 0x72, 0x74, 0x66 } } // {\rtf
    };

    // MIME type to extension mapping for validation
    private static readonly Dictionary<string, string[]> MimeTypeExtensions = new()
    {
        ["application/pdf"] = new[] { ".pdf" },
        ["application/msword"] = new[] { ".doc" },
        ["application/vnd.openxmlformats-officedocument.wordprocessingml.document"] = new[] { ".docx" },
        ["application/vnd.ms-excel"] = new[] { ".xls" },
        ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"] = new[] { ".xlsx" },
        ["application/vnd.ms-powerpoint"] = new[] { ".ppt" },
        ["application/vnd.openxmlformats-officedocument.presentationml.presentation"] = new[] { ".pptx" },
        ["text/plain"] = new[] { ".txt" },
        ["application/rtf"] = new[] { ".rtf" },
        ["image/jpeg"] = new[] { ".jpg", ".jpeg" },
        ["image/png"] = new[] { ".png" },
        ["image/gif"] = new[] { ".gif" },
        ["image/bmp"] = new[] { ".bmp" },
        ["application/zip"] = new[] { ".zip" },
        ["application/x-rar-compressed"] = new[] { ".rar" },
        ["application/x-7z-compressed"] = new[] { ".7z" }
    };

    public FileContentValidationService(ILogger<FileContentValidationService> logger)
    {
        _logger = logger;
    }

    public async Task<FileValidationResult> ValidateFileContentAsync(IFormFile file)
    {
        try
        {
            var result = new FileValidationResult
            {
                FileSize = file.Length,
                DetectedContentType = file.ContentType
            };

            using var stream = file.OpenReadStream();
            
            // Read first 32 bytes for magic number detection
            var buffer = new byte[32];
            var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
            
            if (bytesRead == 0)
            {
                result.AddError("File is empty or cannot be read");
                return result;
            }

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            result.DetectedExtension = extension;

            // Validate magic numbers
            var magicNumberValid = ValidateMagicNumbers(buffer, extension);
            result.PassedMagicNumberCheck = magicNumberValid;

            if (!magicNumberValid)
            {
                result.AddError($"File content does not match the expected format for {extension} files");
            }

            // Validate MIME type consistency
            var mimeTypeValid = ValidateMimeTypeConsistency(file.ContentType, extension);
            if (!mimeTypeValid)
            {
                result.AddError($"MIME type '{file.ContentType}' does not match file extension '{extension}'");
            }

            // Additional content-based validation
            stream.Position = 0;
            var additionalValidation = await PerformAdditionalContentValidationAsync(stream, extension);
            if (!additionalValidation.IsValid)
            {
                result.Errors.AddRange(additionalValidation.Errors);
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during file content validation for file: {FileName}", file.FileName);
            return FileValidationResult.Failure($"Content validation error: {ex.Message}");
        }
    }

    public async Task<Dictionary<string, FileValidationResult>> ValidateMultipleFileContentsAsync(IEnumerable<IFormFile> files)
    {
        var results = new Dictionary<string, FileValidationResult>();
        
        foreach (var file in files)
        {
            var result = await ValidateFileContentAsync(file);
            results[file.FileName] = result;
        }

        return results;
    }

    private bool ValidateMagicNumbers(byte[] buffer, string extension)
    {
        if (!MagicNumbers.ContainsKey(extension))
        {
            // If we don't have magic numbers defined for this extension, skip validation
            return true;
        }

        var expectedSignatures = MagicNumbers[extension];
        
        // If no signatures defined (like for .txt), skip validation
        if (expectedSignatures.Count == 0)
        {
            return true;
        }

        foreach (var signature in expectedSignatures)
        {
            if (buffer.Length >= signature.Length)
            {
                var matches = true;
                for (int i = 0; i < signature.Length; i++)
                {
                    if (buffer[i] != signature[i])
                    {
                        matches = false;
                        break;
                    }
                }
                
                if (matches)
                {
                    return true;
                }
            }
        }

        return false;
    }

    private bool ValidateMimeTypeConsistency(string mimeType, string extension)
    {
        if (string.IsNullOrEmpty(mimeType) || string.IsNullOrEmpty(extension))
        {
            return false;
        }

        var normalizedMimeType = mimeType.ToLowerInvariant();
        
        if (MimeTypeExtensions.ContainsKey(normalizedMimeType))
        {
            var validExtensions = MimeTypeExtensions[normalizedMimeType];
            return validExtensions.Contains(extension);
        }

        // If MIME type is not in our mapping, allow it (might be a valid but uncommon type)
        return true;
    }

    private async Task<FileValidationResult> PerformAdditionalContentValidationAsync(Stream stream, string extension)
    {
        var result = new FileValidationResult { IsValid = true };

        try
        {
            switch (extension)
            {
                case ".pdf":
                    result = await ValidatePdfContentAsync(stream);
                    break;
                case ".jpg":
                case ".jpeg":
                    result = await ValidateJpegContentAsync(stream);
                    break;
                case ".png":
                    result = await ValidatePngContentAsync(stream);
                    break;
                default:
                    // No additional validation for other file types
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Additional content validation failed for extension: {Extension}", extension);
            result.AddError($"Additional validation failed: {ex.Message}");
        }

        return result;
    }

    private async Task<FileValidationResult> ValidatePdfContentAsync(Stream stream)
    {
        var result = new FileValidationResult { IsValid = true };
        
        try
        {
            var buffer = new byte[1024];
            await stream.ReadAsync(buffer, 0, buffer.Length);
            var content = System.Text.Encoding.ASCII.GetString(buffer);
            
            // Check for PDF version in header
            if (!content.Contains("%PDF-"))
            {
                result.AddError("Invalid PDF header");
            }
        }
        catch (Exception ex)
        {
            result.AddError($"PDF validation error: {ex.Message}");
        }

        return result;
    }

    private async Task<FileValidationResult> ValidateJpegContentAsync(Stream stream)
    {
        var result = new FileValidationResult { IsValid = true };
        
        try
        {
            // Read first few bytes to validate JPEG structure
            var buffer = new byte[4];
            await stream.ReadAsync(buffer, 0, buffer.Length);
            
            // JPEG files should start with FF D8 and end with FF D9
            if (buffer[0] != 0xFF || buffer[1] != 0xD8)
            {
                result.AddError("Invalid JPEG header");
            }
        }
        catch (Exception ex)
        {
            result.AddError($"JPEG validation error: {ex.Message}");
        }

        return result;
    }

    private async Task<FileValidationResult> ValidatePngContentAsync(Stream stream)
    {
        var result = new FileValidationResult { IsValid = true };
        
        try
        {
            var buffer = new byte[8];
            await stream.ReadAsync(buffer, 0, buffer.Length);
            
            // PNG signature: 89 50 4E 47 0D 0A 1A 0A
            var pngSignature = new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A };
            
            for (int i = 0; i < pngSignature.Length; i++)
            {
                if (buffer[i] != pngSignature[i])
                {
                    result.AddError("Invalid PNG signature");
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            result.AddError($"PNG validation error: {ex.Message}");
        }

        return result;
    }
}
