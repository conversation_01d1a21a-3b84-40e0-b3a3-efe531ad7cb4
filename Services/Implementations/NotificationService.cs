using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Models.Dtos;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using Rlx.Shared.Interfaces;

namespace AcademicPerformance.Services.Implementations
{
    /// <summary>
    /// Email notification service implementation
    /// MailKit kullanarak SMTP email gönderimi
    /// </summary>
    public class NotificationService : INotificationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<NotificationService> _logger;
        private readonly IRlxSystemLogHelper<NotificationService> _systemLogHelper;
        private readonly IEmailTemplateService _templateService;
        private readonly SmtpConfigurationDto _smtpConfig;

        public NotificationService(
            IConfiguration configuration,
            ILogger<NotificationService> logger,
            IRlxSystemLogHelper<NotificationService> systemLogHelper,
            IEmailTemplateService templateService)
        {
            _configuration = configuration;
            _logger = logger;
            _systemLogHelper = systemLogHelper;
            _templateService = templateService;
            _smtpConfig = LoadSmtpConfiguration();
        }

        #region Email Operations

        public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = false)
        {
            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(_smtpConfig.FromName, _smtpConfig.FromEmail));
                message.To.Add(new MailboxAddress("", to));
                message.Subject = subject;

                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                {
                    bodyBuilder.HtmlBody = body;
                }
                else
                {
                    bodyBuilder.TextBody = body;
                }
                message.Body = bodyBuilder.ToMessageBody();

                using var client = new SmtpClient();
                await client.ConnectAsync(_smtpConfig.Host, _smtpConfig.Port, _smtpConfig.EnableSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(_smtpConfig.Username, _smtpConfig.Password);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                await _systemLogHelper.LogInfoAsync($"Email başarıyla gönderildi: {to} - {subject}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Email gönderim hatası: {To} - {Subject}", to, subject);
                await _systemLogHelper.LogErrorAsync($"Email gönderim hatası: {to} - {subject}", ex);
                return false;
            }
        }

        public async Task<bool> SendTemplatedEmailAsync(string to, string templateName, object templateData)
        {
            try
            {
                if (!_templateService.TemplateExists(templateName))
                {
                    _logger.LogError("Template bulunamadı: {TemplateName}", templateName);
                    return false;
                }

                var (subject, body) = await _templateService.RenderTemplateAsync(templateName, templateData);
                return await SendEmailAsync(to, subject, body, false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Template email gönderim hatası: {To} - {TemplateName}", to, templateName);
                return false;
            }
        }

        public async Task<int> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = false)
        {
            int successCount = 0;
            
            foreach (var recipient in recipients)
            {
                if (await SendEmailAsync(recipient, subject, body, isHtml))
                {
                    successCount++;
                }
                
                // Rate limiting - 100ms delay between emails
                await Task.Delay(100);
            }

            await _systemLogHelper.LogInfoAsync($"Toplu email gönderimi tamamlandı: {successCount}/{recipients.Count} başarılı");
            return successCount;
        }

        #endregion

        #region Workflow Notifications

        public async Task<bool> SendSubmissionApprovedNotificationAsync(string submissionId, string academicianEmail, string controllerName)
        {
            var templateData = new SubmissionApprovedTemplateData
            {
                AcademicianName = "Akademisyen", // Bu bilgi submission'dan alınacak
                SubmissionId = submissionId,
                FormName = "Form", // Bu bilgi submission'dan alınacak
                ControllerName = controllerName,
                ApprovalDate = DateTime.Now,
                ApprovalNotes = null
            };

            return await SendTemplatedEmailAsync(academicianEmail, "SubmissionApproved", templateData);
        }

        public async Task<bool> SendSubmissionRejectedNotificationAsync(string submissionId, string academicianEmail, string controllerName, string rejectionReason)
        {
            var templateData = new SubmissionRejectedTemplateData
            {
                AcademicianName = "Akademisyen", // Bu bilgi submission'dan alınacak
                SubmissionId = submissionId,
                FormName = "Form", // Bu bilgi submission'dan alınacak
                ControllerName = controllerName,
                RejectionDate = DateTime.Now,
                RejectionReason = rejectionReason,
                RevisionInstructions = null
            };

            return await SendTemplatedEmailAsync(academicianEmail, "SubmissionRejected", templateData);
        }

        public async Task<bool> SendNewSubmissionNotificationAsync(string submissionId, string controllerEmail, string academicianName)
        {
            var templateData = new NewSubmissionTemplateData
            {
                ControllerName = "Controller", // Bu bilgi user'dan alınacak
                AcademicianName = academicianName,
                SubmissionId = submissionId,
                FormName = "Form", // Bu bilgi submission'dan alınacak
                SubmissionDate = DateTime.Now,
                Department = null
            };

            return await SendTemplatedEmailAsync(controllerEmail, "NewSubmission", templateData);
        }

        public async Task<bool> SendRevisionRequestNotificationAsync(string submissionId, string academicianEmail, string revisionNotes, DateTime deadline)
        {
            var templateData = new RevisionRequestTemplateData
            {
                AcademicianName = "Akademisyen", // Bu bilgi submission'dan alınacak
                SubmissionId = submissionId,
                FormName = "Form", // Bu bilgi submission'dan alınacak
                RevisionNotes = revisionNotes,
                Deadline = deadline,
                ControllerName = "Controller"
            };

            return await SendTemplatedEmailAsync(academicianEmail, "RevisionRequest", templateData);
        }

        public async Task<bool> SendDeadlineReminderNotificationAsync(string submissionId, string academicianEmail, int daysRemaining)
        {
            var templateData = new DeadlineReminderTemplateData
            {
                AcademicianName = "Akademisyen", // Bu bilgi submission'dan alınacak
                SubmissionId = submissionId,
                FormName = "Form", // Bu bilgi submission'dan alınacak
                DaysRemaining = daysRemaining,
                Deadline = DateTime.Now.AddDays(daysRemaining)
            };

            return await SendTemplatedEmailAsync(academicianEmail, "DeadlineReminder", templateData);
        }

        #endregion

        #region Specialized Workflow Notifications

        public async Task<bool> SendPortfolioVerificationNotificationAsync(string courseId, string archivistEmail, string academicianName)
        {
            var templateData = new PortfolioVerificationTemplateData
            {
                ArchivistName = "Archivist", // Bu bilgi user'dan alınacak
                AcademicianName = academicianName,
                CourseId = courseId,
                CourseName = "Ders Adı", // Bu bilgi course'dan alınacak
                Period = "2024-2025 Güz",
                VerificationDate = DateTime.Now
            };

            return await SendTemplatedEmailAsync(archivistEmail, "PortfolioVerification", templateData);
        }

        public async Task<bool> SendStaffCompetencyEvaluationNotificationAsync(string evaluationId, string managerEmail, string staffName)
        {
            var templateData = new StaffCompetencyEvaluationTemplateData
            {
                ManagerName = "Manager", // Bu bilgi user'dan alınacak
                StaffName = staffName,
                EvaluationId = evaluationId,
                EvaluationPeriod = "2024",
                EvaluationDate = DateTime.Now,
                Department = null
            };

            return await SendTemplatedEmailAsync(managerEmail, "StaffCompetencyEvaluation", templateData);
        }

        public async Task<bool> SendDepartmentPerformanceNotificationAsync(string departmentId, string strategicOfficeEmail, string period)
        {
            var templateData = new DepartmentPerformanceTemplateData
            {
                StrategicOfficeName = "Stratejik Ofis", // Bu bilgi user'dan alınacak
                DepartmentName = "Bölüm Adı", // Bu bilgi department'dan alınacak
                Period = period,
                DataEntryDate = DateTime.Now,
                Notes = null
            };

            return await SendTemplatedEmailAsync(strategicOfficeEmail, "DepartmentPerformance", templateData);
        }

        #endregion

        #region Health Check

        public async Task<bool> TestSmtpConnectionAsync()
        {
            try
            {
                using var client = new SmtpClient();
                await client.ConnectAsync(_smtpConfig.Host, _smtpConfig.Port, _smtpConfig.EnableSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(_smtpConfig.Username, _smtpConfig.Password);
                await client.DisconnectAsync(true);

                await _systemLogHelper.LogInfoAsync("SMTP bağlantı testi başarılı");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SMTP bağlantı testi başarısız");
                await _systemLogHelper.LogErrorAsync("SMTP bağlantı testi başarısız", ex);
                return false;
            }
        }

        public async Task<bool> SendTestEmailAsync(string testEmail)
        {
            var templateData = new { TestDate = DateTime.Now.ToString("dd.MM.yyyy HH:mm") };
            return await SendTemplatedEmailAsync(testEmail, "TestEmail", templateData);
        }

        #endregion

        #region Private Methods

        private SmtpConfigurationDto LoadSmtpConfiguration()
        {
            return new SmtpConfigurationDto
            {
                Host = _configuration["EmailSettings:SmtpHost"] ?? "smtp.gmail.com",
                Port = int.Parse(_configuration["EmailSettings:SmtpPort"] ?? "587"),
                EnableSsl = bool.Parse(_configuration["EmailSettings:EnableSsl"] ?? "true"),
                Username = _configuration["EmailSettings:Username"] ?? "",
                Password = _configuration["EmailSettings:Password"] ?? "",
                FromEmail = _configuration["EmailSettings:FromEmail"] ?? "",
                FromName = _configuration["EmailSettings:FromName"] ?? "APDYS Notification System"
            };
        }

        #endregion
    }
}
