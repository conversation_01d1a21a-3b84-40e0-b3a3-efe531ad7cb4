using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Interfaces;

namespace AcademicPerformance.Services.Implementations
{
    public class UserDataService : IUserDataService
    {
        private readonly IOrganizationManagementApiService _organizationApiService;
        private readonly IRlxIdentitySharedManager _identityManager;
        private readonly ILogger<UserDataService> _logger;

        public UserDataService(
            IOrganizationManagementApiService organizationApiService,
            IRlxIdentitySharedManager identityManager,
            ILogger<UserDataService> logger)
        {
            _organizationApiService = organizationApiService;
            _identityManager = identityManager;
            _logger = logger;
        }

        public async Task<UserProfileDto> GetUserProfileAsync(string userId)
        {
            try
            {
                // GEÇİCİ: Test için mock veri
                var userProfile = new UserProfileDto
                {
                    UserId = userId,
                    UserName = userId,
                    FullName = "Test User " + userId,
                    Email = $"test.user.{userId}@arel.edu.tr",
                    Department = "Computer Engineering",
                    AcademicCadre = "AssocProf",
                    Roles = new List<string> { "Academician" },
                    Permissions = new List<string> { "permission.academician.all" }
                };

                return await Task.FromResult(userProfile);

                // ORİJİNAL KOD (test için yorum satırında):
                /*
                var userProfile = new UserProfileDto
                {
                    UserId = userId
                };

                // Identity Server'dan kullanıcı rolleri ve claim'leri al
                var rolesClaims = await _identityManager.GetUsersRolesClaims(userId);
                if (rolesClaims != null)
                {
                    var roles = rolesClaims.Where(c => c.ClaimType == "role").Select(c => c.ClaimValue).ToList();
                    var permissions = rolesClaims.Where(c => c.ClaimType.StartsWith("permission")).Select(c => c.ClaimValue).ToList();

                    userProfile.Roles = roles;
                    userProfile.Permissions = permissions;

                    userProfile.UserName = userId;
                    userProfile.FullName = "User " + userId;
                    userProfile.Email = $"user{userId}@arel.edu.tr";
                }

                userProfile.Department = await GetUserDepartmentAsync(userId);
                userProfile.AcademicCadre = await GetUserAcademicCadreAsync(userId);

                return userProfile;
                */
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user profile for user {UserId}", userId);
                throw;
            }
        }

        public async Task<UserContextCo> GetUserContextAsync(string userId)
        {
            try
            {
                var userContext = new UserContextCo
                {
                    UserId = userId,
                    FullName = "User " + userId,
                    Email = $"user{userId}@arel.edu.tr"
                };

                // OrganizationManagement'dan kullanıcı departman bilgilerini al
                var departments = await GetUserDepartmentsAsync(userId);
                userContext.Departments = departments.ToArray();

                userContext.AcademicCadre = await GetUserAcademicCadreAsync(userId);

                return userContext;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user context for user {UserId}", userId);
                throw;
            }
        }

        public async Task<string> GetUserDepartmentAsync(string userId)
        {
            try
            {
                // Organization Management API için string userId'yi int'e çevir
                if (!int.TryParse(userId, out int userIdInt))
                {
                    _logger.LogWarning("Could not convert userId {UserId} to integer for Organization Management lookup", userId);
                    return "Unknown Department"; // Fallback değer
                }

                var personUsers = await _organizationApiService.GetPersonsAsync();
                var personUser = personUsers.FirstOrDefault(p =>
                    p.PersonUsers?.Any(pu => pu.UserId == userIdInt) == true);

                if (personUser == null)
                {
                    _logger.LogWarning("No person found for user {UserId} in Organization Management", userId);
                    return "Unknown Department"; // Fallback değer
                }

                var personPositions = await _organizationApiService.GetPersonPositionsByPersonIdAsync(personUser.AutoIncrementId ?? 0);
                var primaryPosition = personPositions.FirstOrDefault(pp =>
                    pp.EndDate == null || pp.EndDate > DateTime.UtcNow);

                if (primaryPosition?.Position?.Unit == null)
                {
                    _logger.LogWarning("No primary position or department found for person {PersonId}", personUser.AutoIncrementId);
                    return "Unknown Department"; // Fallback değer
                }

                return primaryPosition.Position.Unit.Name ?? "Unknown Department";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving department for user {UserId}", userId);
                return "Unknown Department"; // Hata durumunda fallback
            }
        }

        public async Task<string> GetUserAcademicCadreAsync(string userId)
        {
            try
            {
                // Organization Management API için string userId'yi int'e çevir
                if (!int.TryParse(userId, out int userIdInt))
                {
                    _logger.LogWarning("Could not convert userId {UserId} to integer for Organization Management lookup", userId);
                    return "Unknown Position"; // Fallback değer
                }

                var personUsers = await _organizationApiService.GetPersonsAsync();
                var personUser = personUsers.FirstOrDefault(p =>
                    p.PersonUsers?.Any(pu => pu.UserId == userIdInt) == true);

                if (personUser == null)
                {
                    _logger.LogWarning("No person found for user {UserId} in Organization Management", userId);
                    return "Unknown Position"; // Fallback değer
                }

                var personPositions = await _organizationApiService.GetPersonPositionsByPersonIdAsync(personUser.AutoIncrementId ?? 0);
                var primaryPosition = personPositions.FirstOrDefault(pp =>
                    pp.EndDate == null || pp.EndDate > DateTime.UtcNow);

                if (primaryPosition?.Position == null)
                {
                    _logger.LogWarning("No primary position found for person {PersonId}", personUser.AutoIncrementId);
                    return "Unknown Position"; // Fallback değer
                }

                return primaryPosition.Position.Name ?? "Unknown Position";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving academic cadre for user {UserId}", userId);
                return "Unknown Position"; // Hata durumunda fallback
            }
        }

        public async Task<List<UserDepartmentDto>> GetUserDepartmentsAsync(string userId)
        {
            try
            {
                // Organization Management API için string userId'yi int'e çevir
                if (!int.TryParse(userId, out int userIdInt))
                {
                    _logger.LogWarning("Could not convert userId {UserId} to integer for Organization Management lookup", userId);
                    // Fallback: boş liste yerine default department döndür
                    return new List<UserDepartmentDto>
                    {
                        new UserDepartmentDto
                        {
                            DepartmentId = "0",
                            DepartmentName = "Unknown Department",
                            IsPrimary = true
                        }
                    };
                }

                var personUsers = await _organizationApiService.GetPersonsAsync();
                var personUser = personUsers.FirstOrDefault(p =>
                    p.PersonUsers?.Any(pu => pu.UserId == userIdInt) == true);

                if (personUser?.PersonPositions == null)
                {
                    _logger.LogWarning("No person positions found for user {UserId}", userId);
                    // Fallback: default department döndür
                    return new List<UserDepartmentDto>
                    {
                        new UserDepartmentDto
                        {
                            DepartmentId = "0",
                            DepartmentName = "Unknown Department",
                            IsPrimary = true
                        }
                    };
                }

                var departments = new List<UserDepartmentDto>();
                foreach (var personPosition in personUser.PersonPositions)
                {
                    if (personPosition.Position?.Unit != null)
                    {
                        departments.Add(new UserDepartmentDto
                        {
                            DepartmentId = personPosition.Position.Unit.Id?.ToString() ?? "0",
                            DepartmentName = personPosition.Position.Unit.Name ?? "Unknown Department",
                            IsPrimary = personPosition.IsPrimary,
                            PositionId = personPosition.Position.Id?.ToString() ?? "0",
                            PositiomName = personPosition.Position.Name ?? "Unknown Position",
                            UnitId = personPosition.Position.Unit.Id?.ToString() ?? "0",
                            UnitName = personPosition.Position.Unit.Name ?? "Unknown Unit"
                        });
                    }
                }

                // Eğer hiç department bulunamazsa fallback döndür
                if (!departments.Any())
                {
                    departments.Add(new UserDepartmentDto
                    {
                        DepartmentId = "0",
                        DepartmentName = "Unknown Department",
                        IsPrimary = true
                    });
                }

                return departments;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving departments for user {UserId}", userId);
                // Hata durumunda fallback department döndür
                return new List<UserDepartmentDto>
                {
                    new UserDepartmentDto
                    {
                        DepartmentId = "0",
                        DepartmentName = "Unknown Department",
                        IsPrimary = true
                    }
                };
            }
        }
    }
}
