using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using System.Text.Json;

namespace AcademicPerformance.Services.Implementations
{
    public class ArelBridgeApiService : IArelBridgeApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ArelBridgeApiService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public ArelBridgeApiService(HttpClient httpClient, ILogger<ArelBridgeApiService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<IEnumerable<CourseInformationDto>?> GetCoursesByAcademicianTcAsync(string academicianTc)
        {
            try
            {
                var url = $"api/v1/course-information/by-academician/{Uri.EscapeDataString(academicianTc)}";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<IEnumerable<CourseInformationDto>>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting courses for academician: {AcademicianTc}", academicianTc);
                return null;
            }
        }

        public async Task<IEnumerable<CourseInformationDto>?> GetCoursesByPeriodAsync(string period)
        {
            try
            {
                var url = $"api/v1/course-information/by-period/{Uri.EscapeDataString(period)}";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<IEnumerable<CourseInformationDto>>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting courses for period: {Period}", period);
                return null;
            }
        }

        public async Task<IEnumerable<CourseInformationDto>?> GetCoursesByCourseCodeAsync(string courseCode, string? period = null)
        {
            try
            {
                var url = $"api/v1/course-information/by-course-code/{Uri.EscapeDataString(courseCode)}";
                if (!string.IsNullOrEmpty(period))
                {
                    url += $"?period={Uri.EscapeDataString(period)}";
                }

                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<IEnumerable<CourseInformationDto>>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting courses by course code: {CourseCode}", courseCode);
                return null;
            }
        }

        public async Task<IEnumerable<string>?> GetActivePeriodsAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/v1/course-information/active-periods");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<IEnumerable<string>>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active periods");
                return null;
            }
        }

        public async Task<(bool IsConnected, string Message)> TestConnectionAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/v1/health");

                if (response.IsSuccessStatusCode)
                {
                    return (true, "ArelBridge connection successful");
                }
                else
                {
                    return (false, $"ArelBridge returned status code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"Connection error: {ex.Message}");
            }
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                var (isConnected, _) = await TestConnectionAsync();
                return isConnected;
            }
            catch
            {
                return false;
            }
        }
    }
}
