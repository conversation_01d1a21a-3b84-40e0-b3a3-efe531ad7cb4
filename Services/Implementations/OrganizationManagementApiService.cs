using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using System.Text.Json;

namespace AcademicPerformance.Services.Implementations
{
    public class OrganizationManagementApiService : IOrganizationManagementApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<OrganizationManagementApiService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public OrganizationManagementApiService(
            IHttpClientFactory httpClientFactory,
            ILogger<OrganizationManagementApiService> logger)
        {
            _httpClient = httpClientFactory.CreateClient("OrganizationManagementApi");
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<PersonDto?> GetPersonByIdAsync(string personId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"persons/{personId}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<PersonDto>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting person by id: {PersonId}", personId);
                return null;
            }
        }

        public async Task<PersonDto?> GetPersonByIdentityNumberAsync(string identityNumber)
        {
            try
            {
                var response = await _httpClient.GetAsync($"persons/by-identity/{identityNumber}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<PersonDto>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting person by identity number: {IdentityNumber}", identityNumber);
                return null;
            }
        }

        public async Task<List<PersonDto>> GetPersonsAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("persons");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<PersonDto>>(content, _jsonOptions) ?? new List<PersonDto>();
                }
                return new List<PersonDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting persons");
                return new List<PersonDto>();
            }
        }

        public async Task<List<PersonUserDto>> GetPersonUsersByPersonIdAsync(int personId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"persons/{personId}/users");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<PersonUserDto>>(content, _jsonOptions) ?? new List<PersonUserDto>();
                }
                return new List<PersonUserDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting person users by person id: {PersonId}", personId);
                return new List<PersonUserDto>();
            }
        }

        public async Task<List<PersonPositionDto>> GetPersonPositionsByPersonIdAsync(int personId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"persons/{personId}/positions");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<PersonPositionDto>>(content, _jsonOptions) ?? new List<PersonPositionDto>();
                }
                return new List<PersonPositionDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting person positions by person id: {PersonId}", personId);
                return new List<PersonPositionDto>();
            }
        }

        public async Task<UnitDto?> GetUnitByIdAsync(string unitId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"units/{unitId}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<UnitDto>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unit by id: {UnitId}", unitId);
                return null;
            }
        }

        public async Task<List<UnitDto>> GetUnitsAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("units");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<UnitDto>>(content, _jsonOptions) ?? new List<UnitDto>();
                }
                return new List<UnitDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting units");
                return new List<UnitDto>();
            }
        }

        public async Task<List<UnitDto>> GetUnitsByParentIdAsync(int? parentId)
        {
            try
            {
                var url = parentId.HasValue ? $"units/by-parent/{parentId}" : "units/root";
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<UnitDto>>(content, _jsonOptions) ?? new List<UnitDto>();
                }
                return new List<UnitDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting units by parent id: {ParentId}", parentId);
                return new List<UnitDto>();
            }
        }

        public async Task<PositionDto?> GetPositionByIdAsync(string positionId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"positions/{positionId}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<PositionDto>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting position by id: {PositionId}", positionId);
                return null;
            }
        }

        public async Task<List<PositionDto>> GetPositionsByUnitIdAsync(int unitId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"units/{unitId}/positions");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<PositionDto>>(content, _jsonOptions) ?? new List<PositionDto>();
                }
                return new List<PositionDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting positions by unit id: {UnitId}", unitId);
                return new List<PositionDto>();
            }
        }

        public async Task<CountryDto?> GetCountryByIdAsync(string countryId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"countries/{countryId}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<CountryDto>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting country by id: {CountryId}", countryId);
                return null;
            }
        }

        public async Task<List<CountryDto>> GetCountriesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("countries");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<CountryDto>>(content, _jsonOptions) ?? new List<CountryDto>();
                }
                return new List<CountryDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting countries");
                return new List<CountryDto>();
            }
        }

        public async Task<StateDto?> GetStateByIdAsync(string stateId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"states/{stateId}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<StateDto>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting state by id: {StateId}", stateId);
                return null;
            }
        }

        public async Task<List<StateDto>> GetStatesByCountryIdAsync(int countryId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"countries/{countryId}/states");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<StateDto>>(content, _jsonOptions) ?? new List<StateDto>();
                }
                return new List<StateDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting states by country id: {CountryId}", countryId);
                return new List<StateDto>();
            }
        }

        public async Task<CityDto?> GetCityByIdAsync(string cityId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"cities/{cityId}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<CityDto>(content, _jsonOptions);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting city by id: {CityId}", cityId);
                return null;
            }
        }

        public async Task<List<CityDto>> GetCitiesByStateIdAsync(int stateId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"states/{stateId}/cities");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<List<CityDto>>(content, _jsonOptions) ?? new List<CityDto>();
                }
                return new List<CityDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cities by state id: {StateId}", stateId);
                return new List<CityDto>();
            }
        }
    }
}
