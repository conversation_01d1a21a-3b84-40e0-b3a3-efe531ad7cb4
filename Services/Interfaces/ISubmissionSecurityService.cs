namespace AcademicPerformance.Services.Interfaces
{
    /// <summary>
    /// Submission ownership validation ve authorization kontrolü
    /// </summary>
    public interface ISubmissionSecurityService
    {
        /// <summary>
        /// Kullanıcının submission'a erişim yetkisi var mı kontrol et
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="action">Yapılmak istenen action (read, write, approve, etc.)</param>
        /// <returns>Erişim yetkisi var mı?</returns>
        Task<bool> CanUserAccessSubmissionAsync(string userId, string submissionId, string action);

        /// <summary>
        /// Controller'ın submission'a erişim yetkisi var mı kontrol et
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns><PERSON><PERSON><PERSON><PERSON> yetkisi var mı?</returns>
        Task<bool> IsControllerAuthorizedForSubmissionAsync(string controllerId, string submissionId);

        /// <summary>
        /// Submission ownership kontrolü
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Kullanıcı submission'ın sahibi mi?</returns>
        Task<bool> IsSubmissionOwnerAsync(string userId, string submissionId);

        /// <summary>
        /// Submission status geçişinin geçerli olup olmadığını kontrol et
        /// </summary>
        /// <param name="currentStatus">Mevcut status</param>
        /// <param name="newStatus">Yeni status</param>
        /// <param name="userRole">Kullanıcı rolü</param>
        /// <returns>Geçiş geçerli mi?</returns>
        Task<bool> IsValidStatusTransitionAsync(string currentStatus, string newStatus, string userRole);
    }
}
