using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;

namespace AcademicPerformance.Services.Interfaces
{
    /// <summary>
    /// Statik kriterler için veri sağlayıcı servisi interface'i
    /// ArelBridge'den statik kriter verilerini çekmek ve AcademicPerformance formatına dönüştürmek için kullanılır
    /// </summary>
    public interface IStaticCriterionDataProvider
    {
        /// <summary>
        /// Belirli bir akademisyen için belirli statik kriterlerin verilerini getirir
        /// </summary>
        /// <param name="academicianId">Akademisyen ID'si</param>
        /// <param name="staticCriterionIds">Statik kriter ID'leri listesi</param>
        /// <returns>Statik kriter verileri</returns>
        Task<List<StaticCriterionDataDto>> GetStaticCriterionDataAsync(string academicianId, List<string> staticCriterionIds);

        /// <summary>
        /// Belirli bir akademisyen için tüm ArelBridge statik kriterlerinin verilerini getirir
        /// </summary>
        /// <param name="academicianId">Akademisyen ID'si</param>
        /// <returns>Tüm ArelBridge statik kriter verileri</returns>
        Task<List<StaticCriterionDataDto>> GetAllArelBridgeStaticCriterionDataAsync(string academicianId);

        /// <summary>
        /// Belirli bir akademisyen için belirli bir statik kriterin verisini getirir
        /// </summary>
        /// <param name="academicianId">Akademisyen ID'si</param>
        /// <param name="staticCriterionId">Statik kriter ID'si</param>
        /// <returns>Statik kriter verisi</returns>
        Task<StaticCriterionDataDto?> GetSingleStaticCriterionDataAsync(string academicianId, string staticCriterionId);

        /// <summary>
        /// Birden fazla akademisyen için belirli statik kriterlerin verilerini toplu olarak getirir
        /// </summary>
        /// <param name="academicianIds">Akademisyen ID'leri listesi</param>
        /// <param name="staticCriterionIds">Statik kriter ID'leri listesi</param>
        /// <returns>Toplu statik kriter verileri</returns>
        Task<Dictionary<string, List<StaticCriterionDataDto>>> GetBatchStaticCriterionDataAsync(List<string> academicianIds, List<string> staticCriterionIds);

        /// <summary>
        /// ArelBridge kriter eşleme konfigürasyonlarını getirir ve günceller
        /// </summary>
        /// <returns>Güncellenmiş kriter eşleme konfigürasyonları</returns>
        Task<List<ArelBridgeCriterionMappingDto>> RefreshCriterionMappingsAsync();

        /// <summary>
        /// Belirli bir statik kriter için veri kaynağının mevcut olup olmadığını kontrol eder
        /// </summary>
        /// <param name="staticCriterionId">Statik kriter ID'si</param>
        /// <returns>Veri kaynağı mevcut mu?</returns>
        Task<bool> IsDataSourceAvailableAsync(string staticCriterionId);

        /// <summary>
        /// ArelBridge servisinin sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Servis sağlıklı mı?</returns>
        Task<bool> IsServiceHealthyAsync();

        /// <summary>
        /// Statik kriter verilerini doğrular
        /// </summary>
        /// <param name="data">Doğrulanacak veri</param>
        /// <returns>Doğrulama sonucu</returns>
        Task<(bool IsValid, List<string> ValidationErrors)> ValidateStaticCriterionDataAsync(StaticCriterionDataDto data);

        /// <summary>
        /// Statik kriter verilerini AcademicPerformance formatına dönüştürür
        /// </summary>
        /// <param name="arelBridgeData">ArelBridge formatındaki veri</param>
        /// <param name="mappings">Kriter eşleme konfigürasyonları</param>
        /// <returns>AcademicPerformance formatına dönüştürülmüş veriler</returns>
        Task<List<StaticCriterionDataDto>> ConvertArelBridgeDataToStaticCriterionDataAsync(
            ArelBridgeAcademicEducationActivityResponseDto arelBridgeData,
            List<ArelBridgeCriterionMappingDto> mappings);

        /// <summary>
        /// Statik kriter veri sağlayıcı performans metriklerini getirir
        /// </summary>
        /// <returns>Performans metrikleri</returns>
        Task<StaticCriterionDataProviderMetricsDto> GetPerformanceMetricsAsync();
    }

    /// <summary>
    /// Statik kriter verisi DTO'su
    /// AcademicPerformance formatında statik kriter verilerini temsil eder
    /// </summary>
    public class StaticCriterionDataDto
    {
        /// <summary>
        /// Akademisyen ID'si
        /// </summary>
        public required string AcademicianId { get; set; }

        /// <summary>
        /// Statik kriter sistem ID'si (AcademicPerformance formatında)
        /// </summary>
        public required string StaticCriterionSystemId { get; set; }

        /// <summary>
        /// Kriter adı
        /// </summary>
        public required string CriterionName { get; set; }

        /// <summary>
        /// Veri tipi (Integer, Decimal, Boolean, String)
        /// </summary>
        public required string DataType { get; set; }

        /// <summary>
        /// Ham değer (sayı, boolean, string)
        /// </summary>
        public object? RawValue { get; set; }

        /// <summary>
        /// Hesaplanmış puan (null - raporlama sisteminde hesaplanacak)
        /// </summary>
        public decimal? CalculatedScore { get; set; }

        /// <summary>
        /// Katsayı (UI'dan değiştirilebilir, StaticCriterionCoefficients tablosundan gelir)
        /// </summary>
        public decimal? Coefficient { get; set; }

        /// <summary>
        /// Maksimum puan limiti
        /// </summary>
        public decimal? MaximumLimit { get; set; }

        /// <summary>
        /// Veri kaynağı (ARELBRIDGE_ACADEMIC_EDUCATION_ACTIVITY)
        /// </summary>
        public string? DataSource { get; set; }

        /// <summary>
        /// Hesaplama tarihi
        /// </summary>
        public DateTime CalculationDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Veri durumu (Success, Error, NotFound, ValidationError)
        /// </summary>
        public string Status { get; set; } = "Success";

        /// <summary>
        /// Hata mesajı (varsa)
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Detay veriler (JSON formatında)
        /// </summary>
        public string? DetailData { get; set; }

        /// <summary>
        /// Cache'den mi geldi?
        /// </summary>
        public bool IsFromCache { get; set; }

        /// <summary>
        /// Son güncellenme tarihi
        /// </summary>
        public DateTime? LastUpdated { get; set; }
    }

    /// <summary>
    /// Statik kriter veri sağlayıcı performans metrikleri DTO'su
    /// </summary>
    public class StaticCriterionDataProviderMetricsDto
    {
        /// <summary>
        /// Toplam veri talebi sayısı
        /// </summary>
        public long TotalRequests { get; set; }

        /// <summary>
        /// Başarılı veri talebi sayısı
        /// </summary>
        public long SuccessfulRequests { get; set; }

        /// <summary>
        /// Başarısız veri talebi sayısı
        /// </summary>
        public long FailedRequests { get; set; }

        /// <summary>
        /// Cache hit sayısı
        /// </summary>
        public long CacheHits { get; set; }

        /// <summary>
        /// Cache miss sayısı
        /// </summary>
        public long CacheMisses { get; set; }

        /// <summary>
        /// Ortalama yanıt süresi (ms)
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// ArelBridge API çağrı sayısı
        /// </summary>
        public long ArelBridgeApiCalls { get; set; }

        /// <summary>
        /// ArelBridge API hata sayısı
        /// </summary>
        public long ArelBridgeApiErrors { get; set; }

        /// <summary>
        /// Veri dönüştürme hata sayısı
        /// </summary>
        public long DataConversionErrors { get; set; }

        /// <summary>
        /// Veri doğrulama hata sayısı
        /// </summary>
        public long DataValidationErrors { get; set; }

        /// <summary>
        /// Son 24 saatteki toplam istek sayısı
        /// </summary>
        public long RequestsLast24Hours { get; set; }

        /// <summary>
        /// Cache hit oranı (%)
        /// </summary>
        public double CacheHitRate => CacheHits + CacheMisses > 0 ? (double)CacheHits / (CacheHits + CacheMisses) * 100 : 0;

        /// <summary>
        /// Başarı oranı (%)
        /// </summary>
        public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;

        /// <summary>
        /// ArelBridge API başarı oranı (%)
        /// </summary>
        public double ArelBridgeApiSuccessRate => ArelBridgeApiCalls > 0 ? (double)(ArelBridgeApiCalls - ArelBridgeApiErrors) / ArelBridgeApiCalls * 100 : 0;

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Statik kriter veri talep seçenekleri
    /// </summary>
    public class StaticCriterionDataRequestOptions
    {
        /// <summary>
        /// Cache'i kullan
        /// </summary>
        public bool UseCache { get; set; } = true;

        /// <summary>
        /// Cache TTL (dakika)
        /// </summary>
        public int CacheTtlMinutes { get; set; } = 60;

        /// <summary>
        /// Retry sayısı
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Timeout süresi (saniye)
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Detay verilerini dahil et
        /// </summary>
        public bool IncludeDetailData { get; set; } = false;

        /// <summary>
        /// Veri doğrulaması yap
        /// </summary>
        public bool ValidateData { get; set; } = true;

        /// <summary>
        /// Hata durumunda fallback değer kullan
        /// </summary>
        public bool UseFallbackOnError { get; set; } = true;

        /// <summary>
        /// Fallback değeri
        /// </summary>
        public object? FallbackValue { get; set; }
    }
}
