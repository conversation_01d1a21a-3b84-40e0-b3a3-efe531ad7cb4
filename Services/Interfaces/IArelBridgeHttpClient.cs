using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Services.Interfaces
{
    /// <summary>
    /// ArelBridge API'sine HTTP istekleri yapmak için client servis interface'i
    /// Akademik eğitim faaliyetleri statik kriterlerini ArelBridge'den çekmek için kullanılır
    /// </summary>
    public interface IArelBridgeHttpClient
    {
        /// <summary>
        /// Belirli bir akademisyen için akademik eğitim faaliyetleri verilerini ArelBridge'den getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <returns>Akademik eğitim faaliyetleri response DTO'su</returns>
        Task<ArelBridgeAcademicEducationActivityResponseDto?> GetAcademicEducationActivityAsync(string academicianTc);

        /// <summary>
        /// Detaylı parametrelerle akademik eğitim faaliyetleri verilerini ArelBridge'den getirir
        /// </summary>
        /// <param name="request">Akademik eğitim faaliyetleri talep DTO'su</param>
        /// <returns>Akademik eğitim faaliyetleri response DTO'su</returns>
        Task<ArelBridgeAcademicEducationActivityResponseDto?> GetAcademicEducationActivityDetailedAsync(ArelBridgeAcademicEducationActivityRequestDto request);

        /// <summary>
        /// Belirli kriterlerin verilerini ArelBridge'den getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <param name="criterionIds">Kriter ID'leri (A1, A2_1, A3_1, vb.)</param>
        /// <returns>Filtrelenmiş akademik eğitim faaliyetleri response DTO'su</returns>
        Task<ArelBridgeAcademicEducationActivityResponseDto?> GetSpecificCriteriaAsync(string academicianTc, List<string> criterionIds);

        /// <summary>
        /// AcademicPerformance formatına dönüştürülmüş veriyi ArelBridge'den getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <returns>AcademicPerformance formatına dönüştürülmüş veri</returns>
        Task<Dictionary<string, object>?> GetAcademicPerformanceFormatAsync(string academicianTc);

        /// <summary>
        /// Kriter eşleme konfigürasyonlarını ArelBridge'den getirir
        /// </summary>
        /// <returns>Kriter eşleme konfigürasyonları listesi</returns>
        Task<List<ArelBridgeCriterionMappingDto>?> GetCriterionMappingsAsync();

        /// <summary>
        /// ArelBridge servis bağlantısını test eder
        /// </summary>
        /// <returns>Bağlantı testi sonucu</returns>
        Task<(bool IsConnected, string Message)> TestConnectionAsync();

        /// <summary>
        /// Akademik eğitim faaliyetleri istatistiklerini ArelBridge'den getirir
        /// </summary>
        /// <param name="academicianTcs">Akademisyen TC listesi (opsiyonel)</param>
        /// <returns>İstatistik verileri</returns>
        Task<ArelBridgeAcademicEducationActivityStatisticsDto?> GetStatisticsAsync(List<string>? academicianTcs = null);

        /// <summary>
        /// Toplu akademisyen verilerini ArelBridge'den getirir
        /// </summary>
        /// <param name="academicianTcs">Akademisyen TC listesi</param>
        /// <returns>Toplu veri listesi</returns>
        Task<List<ArelBridgeAcademicEducationActivityResponseDto>> GetBatchAcademicEducationActivitiesAsync(List<string> academicianTcs);

        /// <summary>
        /// ArelBridge API'nin sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Sağlık durumu</returns>
        Task<bool> IsHealthyAsync();
    }

    /// <summary>
    /// ArelBridge akademik eğitim faaliyetleri response DTO'su
    /// AcademicPerformance projesi için uyarlanmış versiyon
    /// </summary>
    public class ArelBridgeAcademicEducationActivityResponseDto
    {
        /// <summary>
        /// Akademisyen adı soyadı
        /// </summary>
        public string? FullName { get; set; }

        /// <summary>
        /// Fakülte ve bölüm bilgisi
        /// </summary>
        public string? FacultyDepartment { get; set; }

        /// <summary>
        /// İdari görev bilgisi
        /// </summary>
        public string? AdministrativeRole { get; set; }

        /// <summary>
        /// Ders yükü değeri
        /// </summary>
        public int CourseLoad { get; set; }

        /// <summary>
        /// Ders adedi
        /// </summary>
        public int CourseCount { get; set; }

        /// <summary>
        /// Değerlendirme skoru
        /// </summary>
        public string? EvaluationScore { get; set; }

        /// <summary>
        /// Sıralama
        /// </summary>
        public string? Ranking { get; set; }

        /// <summary>
        /// A bölümü akademik eğitim faaliyetleri toplam puanı (UI'da hesaplanır)
        /// </summary>
        public decimal TotalAcademicEducationScore { get; set; }

        /// <summary>
        /// A1-A13 kriterleri detay verileri
        /// </summary>
        public ArelBridgeAcademicEducationCriteriaDetailsDto Criteria { get; set; } = new();

        /// <summary>
        /// Hesaplama tarihi
        /// </summary>
        public DateTime CalculationDate { get; set; }

        /// <summary>
        /// Hesaplama durumu
        /// </summary>
        public string Status { get; set; } = "Success";

        /// <summary>
        /// Hata mesajı (varsa)
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// ArelBridge A1-A13 kriterleri detay verileri DTO'su
    /// Puan değerleri kaldırıldı, sadece sayı değerleri kullanılıyor
    /// Katsayılar AcademicPerformance'da UI'dan yönetiliyor
    /// </summary>
    public class ArelBridgeAcademicEducationCriteriaDetailsDto
    {
        // A1 - Ders Yükü
        public int A1_Count { get; set; }

        // A2_1 - Öğrenci Değerlendirme Anketi
        public int A2_1_Count { get; set; }

        // A3_1 - Danışmana Atanmış Öğrenci Sayısı
        public int A3_1_Count { get; set; }

        // A3_2 - İletişime Geçtiği Öğrenci Sayısı
        public int A3_2_Count { get; set; }

        // A3_3 - Öğrenci Memnuniyeti
        public int A3_3_Count { get; set; }

        // A3_4 - Danışman Tutundurma Başarısı
        public int A3_4_Count { get; set; }

        // A3_5 - Öğrenci Kulübü Danışmanlığı
        public int A3_5_Count { get; set; }

        // A3_6 - Öğrenci GPA Ortalaması
        public int A3_6_Count { get; set; }

        // A3_7 - Onur Öğrenci Sayısı
        public int A3_7_Count { get; set; }

        // A3_8 - Devamsızlık Oranı
        public int A3_8_Count { get; set; }

        // A3_9 - Erasmus Katılımı
        public int A3_9_Count { get; set; }

        // A3_10 - Sosyal Kulüp Üyeliği
        public int A3_10_Count { get; set; }

        // A3_11 - Spor Kulübü Üyeliği
        public int A3_11_Count { get; set; }

        // A3_12 - Çift Ana Dal/Yan Dal
        public int A3_12_Count { get; set; }

        // A3_13 - Üniversite Takımları
        public int A3_13_Count { get; set; }

        // A3_14 - Teknokent/TTO Projeleri
        public int A3_14_Count { get; set; }

        // A3_15 - TÜBİTAK 2209 Başvuruları
        public int A3_15_Count { get; set; }

        // A4 - Yüksek Lisans Tez Yöneticiliği
        public int A4_Count { get; set; }

        // A5 - Doktora Tez Yöneticiliği
        public int A5_Count { get; set; }
    }

    /// <summary>
    /// ArelBridge akademik eğitim faaliyetleri talep DTO'su
    /// </summary>
    public class ArelBridgeAcademicEducationActivityRequestDto
    {
        /// <summary>
        /// Akademisyen TC Kimlik Numarası
        /// </summary>
        public required string AcademicianTc { get; set; }

        /// <summary>
        /// Hesaplama yılı (opsiyonel)
        /// </summary>
        public int? CalculationYear { get; set; }

        /// <summary>
        /// Belirli kriterler için filtreleme (opsiyonel)
        /// </summary>
        public List<string>? CriterionIds { get; set; }

        /// <summary>
        /// Detay verilerini dahil et
        /// </summary>
        public bool IncludeDetailData { get; set; } = false;
    }

    /// <summary>
    /// ArelBridge kriter eşleme DTO'su
    /// </summary>
    public class ArelBridgeCriterionMappingDto
    {
        /// <summary>
        /// ArelBridge kriter ID'si
        /// </summary>
        public required string ArelBridgeCriterionId { get; set; }

        /// <summary>
        /// AcademicPerformance statik kriter ID'si
        /// </summary>
        public required string AcademicPerformanceStaticCriterionId { get; set; }

        /// <summary>
        /// Kriter adı
        /// </summary>
        public required string CriterionName { get; set; }

        /// <summary>
        /// Veri tipi
        /// </summary>
        public required string DataType { get; set; }

        /// <summary>
        /// Aktif durumu
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// ArelBridge akademik eğitim faaliyetleri istatistikleri DTO'su
    /// </summary>
    public class ArelBridgeAcademicEducationActivityStatisticsDto
    {
        /// <summary>
        /// Toplam akademisyen sayısı
        /// </summary>
        public int TotalAcademicians { get; set; }

        /// <summary>
        /// Ortalama A bölümü puanı
        /// </summary>
        public decimal AverageScore { get; set; }

        /// <summary>
        /// En yüksek A bölümü puanı
        /// </summary>
        public decimal MaxScore { get; set; }

        /// <summary>
        /// En düşük A bölümü puanı
        /// </summary>
        public decimal MinScore { get; set; }

        /// <summary>
        /// Son hesaplama tarihi
        /// </summary>
        public DateTime LastCalculationDate { get; set; }
    }
}
