using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Services.Interfaces
{
    public interface IOrganizationManagementApiService
    {
        // Ki<PERSON>i ile ilgili metotlar
        Task<PersonDto?> GetPersonByIdAsync(string personId);
        Task<PersonDto?> GetPersonByIdentityNumberAsync(string identityNumber);
        Task<List<PersonDto>> GetPersonsAsync();
        Task<List<PersonUserDto>> GetPersonUsersByPersonIdAsync(int personId);
        Task<List<PersonPositionDto>> GetPersonPositionsByPersonIdAsync(int personId);

        // Birim ve Pozisyon ile ilgili metotlar
        Task<UnitDto?> GetUnitByIdAsync(string unitId);
        Task<List<UnitDto>> GetUnitsAsync();
        Task<List<UnitDto>> GetUnitsByParentIdAsync(int? parentId);
        Task<PositionDto?> GetPositionByIdAsync(string positionId);
        Task<List<PositionDto>> GetPositionsByUnitIdAsync(int unitId);

        // Location related methods
        Task<CountryDto?> GetCountryByIdAsync(string countryId);
        Task<List<CountryDto>> GetCountriesAsync();
        Task<StateDto?> GetStateByIdAsync(string stateId);
        Task<List<StateDto>> GetStatesByCountryIdAsync(int countryId);
        Task<CityDto?> GetCityByIdAsync(string cityId);
        Task<List<CityDto>> GetCitiesByStateIdAsync(int stateId);
    }
}
