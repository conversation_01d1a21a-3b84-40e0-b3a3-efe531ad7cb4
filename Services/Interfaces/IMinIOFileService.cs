using AcademicPerformance.Models.Configurations;

namespace AcademicPerformance.Services.Interfaces;

/// <summary>
/// MinIO object storage file operations interface
/// </summary>
public interface IMinIOFileService
{
    /// <summary>
    /// Dosya yükleme - stream ile
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı (dosya yolu)</param>
    /// <param name="stream">Dosya stream'i</param>
    /// <param name="contentType">MIME type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upload sonucu</returns>
    Task<MinIOUploadResult> UploadFileAsync(
        string bucketName,
        string objectName,
        Stream stream,
        string contentType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Dosya yükleme - byte array ile
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="data">Dosya verisi</param>
    /// <param name="contentType">MIME type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upload sonucu</returns>
    Task<MinIOUploadResult> UploadFileAsync(
        string bucketName,
        string objectName,
        byte[] data,
        string contentType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Dosya indirme - stream olarak
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya stream'i</returns>
    Task<Stream> DownloadFileAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Dosya indirme - byte array olarak
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya verisi</returns>
    Task<byte[]> DownloadFileBytesAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Dosya silme
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Silme başarılı mı</returns>
    Task<bool> DeleteFileAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Dosya var mı kontrolü
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya var mı</returns>
    Task<bool> FileExistsAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Dosya metadata'sını alma
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya metadata'sı</returns>
    Task<MinIOFileMetadata?> GetFileMetadataAsync(
        string bucketName,
        string objectName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Presigned URL oluşturma (güvenli download için)
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="objectName">Object adı</param>
    /// <param name="expirySeconds">URL geçerlilik süresi (saniye)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Presigned URL</returns>
    Task<string> GeneratePresignedUrlAsync(
        string bucketName,
        string objectName,
        int expirySeconds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bucket'taki dosyaları listeleme
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="prefix">Dosya prefix'i (klasör gibi)</param>
    /// <param name="recursive">Alt klasörleri de dahil et</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dosya listesi</returns>
    Task<List<MinIOFileInfo>> ListFilesAsync(
        string bucketName,
        string? prefix = null,
        bool recursive = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bucket var mı kontrolü
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bucket var mı</returns>
    Task<bool> BucketExistsAsync(
        string bucketName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bucket oluşturma
    /// </summary>
    /// <param name="bucketName">Bucket adı</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Oluşturma başarılı mı</returns>
    Task<bool> CreateBucketAsync(
        string bucketName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Dosya validasyonu (boyut, tip, vs.)
    /// </summary>
    /// <param name="stream">Dosya stream'i</param>
    /// <param name="fileName">Dosya adı</param>
    /// <param name="contentType">MIME type</param>
    /// <returns>Validation sonucu</returns>
    Task<MinIOValidationResult> ValidateFileAsync(
        Stream stream,
        string fileName,
        string contentType);

    /// <summary>
    /// Unique object name oluşturma
    /// </summary>
    /// <param name="originalFileName">Orijinal dosya adı</param>
    /// <param name="prefix">Prefix (klasör yapısı için)</param>
    /// <returns>Unique object name</returns>
    string GenerateUniqueObjectName(string originalFileName, string? prefix = null);

    /// <summary>
    /// Dosya kopyalama (MinIO içinde)
    /// </summary>
    /// <param name="sourceBucket">Kaynak bucket</param>
    /// <param name="sourceObject">Kaynak object</param>
    /// <param name="destBucket">Hedef bucket</param>
    /// <param name="destObject">Hedef object</param>
    /// <returns>Kopyalama sonucu</returns>
    Task<MinIOUploadResult> CopyFileAsync(string sourceBucket, string sourceObject, string destBucket, string destObject);
}

/// <summary>
/// MinIO upload sonucu
/// </summary>
public class MinIOUploadResult
{
    public bool Success { get; set; }
    public string? ObjectName { get; set; }
    public string? ETag { get; set; }
    public long Size { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// MinIO dosya metadata'sı
/// </summary>
public class MinIOFileMetadata
{
    public string ObjectName { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long Size { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public string? ETag { get; set; }
    public string? Checksum { get; set; }
    public DateTime LastModified { get; set; }
    public Dictionary<string, string> UserMetadata { get; set; } = new();
}

/// <summary>
/// MinIO dosya bilgisi
/// </summary>
public class MinIOFileInfo
{
    public string ObjectName { get; set; } = string.Empty;
    public long Size { get; set; }
    public DateTime LastModified { get; set; }
    public string? ETag { get; set; }
    public bool IsDirectory { get; set; }
}

/// <summary>
/// MinIO dosya validation sonucu
/// </summary>
public class MinIOValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public string? DetectedContentType { get; set; }
    public long FileSize { get; set; }
}
