using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Services.Interfaces
{
    public interface IUserDataService
    {
        Task<UserProfileDto> GetUserProfileAsync(string userId);
        Task<UserContextCo> GetUserContextAsync(string userId);
        Task<string> GetUserDepartmentAsync(string userId);
        Task<string> GetUserAcademicCadreAsync(string userId);
    }
}
