using AcademicPerformance.Models.Configurations;

namespace AcademicPerformance.Services.Interfaces;

/// <summary>
/// Advanced file content validation service interface
/// </summary>
public interface IFileContentValidationService
{
    /// <summary>
    /// Validate file content using magic numbers and content analysis
    /// </summary>
    /// <param name="file">File to validate</param>
    /// <returns>Detailed validation result</returns>
    Task<FileValidationResult> ValidateFileContentAsync(IFormFile file);

    /// <summary>
    /// Validate multiple files content
    /// </summary>
    /// <param name="files">Files to validate</param>
    /// <returns>Dictionary of validation results by filename</returns>
    Task<Dictionary<string, FileValidationResult>> ValidateMultipleFileContentsAsync(IEnumerable<IFormFile> files);
}
