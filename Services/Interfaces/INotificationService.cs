using AcademicPerformance.Models.Dtos;

namespace AcademicPerformance.Services.Interfaces
{
    /// <summary>
    /// Email notification service interface
    /// Akademik performans sistemi için email notification yönetimi
    /// </summary>
    public interface INotificationService
    {
        #region Email Operations

        /// <summary>
        /// Email gönder
        /// </summary>
        /// <param name="to">Alıcı email adresi</param>
        /// <param name="subject">Email konusu</param>
        /// <param name="body">Email içeriği</param>
        /// <param name="isHtml">HTML formatında mı?</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = false);

        /// <summary>
        /// Template kullanarak email gönder
        /// </summary>
        /// <param name="to">Alıcı email adresi</param>
        /// <param name="templateName">Template adı</param>
        /// <param name="templateData">Template verileri</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendTemplatedEmailAsync(string to, string templateName, object templateData);

        /// <summary>
        /// Toplu email gönder
        /// </summary>
        /// <param name="recipients">Alıcı listesi</param>
        /// <param name="subject">Email konusu</param>
        /// <param name="body">Email içeriği</param>
        /// <param name="isHtml">HTML formatında mı?</param>
        /// <returns>Başarılı gönderim sayısı</returns>
        Task<int> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = false);

        #endregion

        #region Workflow Notifications

        /// <summary>
        /// Submission onaylandı notification'ı gönder
        /// </summary>
        /// <param name="submissionId">Submission ID</param>
        /// <param name="academicianEmail">Akademisyen email</param>
        /// <param name="controllerName">Controller adı</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendSubmissionApprovedNotificationAsync(string submissionId, string academicianEmail, string controllerName);

        /// <summary>
        /// Submission reddedildi notification'ı gönder
        /// </summary>
        /// <param name="submissionId">Submission ID</param>
        /// <param name="academicianEmail">Akademisyen email</param>
        /// <param name="controllerName">Controller adı</param>
        /// <param name="rejectionReason">Red nedeni</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendSubmissionRejectedNotificationAsync(string submissionId, string academicianEmail, string controllerName, string rejectionReason);

        /// <summary>
        /// Yeni submission controller'a bildirim gönder
        /// </summary>
        /// <param name="submissionId">Submission ID</param>
        /// <param name="controllerEmail">Controller email</param>
        /// <param name="academicianName">Akademisyen adı</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendNewSubmissionNotificationAsync(string submissionId, string controllerEmail, string academicianName);

        /// <summary>
        /// Revision request notification'ı gönder
        /// </summary>
        /// <param name="submissionId">Submission ID</param>
        /// <param name="academicianEmail">Akademisyen email</param>
        /// <param name="revisionNotes">Revizyon notları</param>
        /// <param name="deadline">Son tarih</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendRevisionRequestNotificationAsync(string submissionId, string academicianEmail, string revisionNotes, DateTime deadline);

        /// <summary>
        /// Deadline reminder notification'ı gönder
        /// </summary>
        /// <param name="submissionId">Submission ID</param>
        /// <param name="academicianEmail">Akademisyen email</param>
        /// <param name="daysRemaining">Kalan gün sayısı</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendDeadlineReminderNotificationAsync(string submissionId, string academicianEmail, int daysRemaining);

        #endregion

        #region Specialized Workflow Notifications

        /// <summary>
        /// Portfolio verification notification'ı gönder
        /// </summary>
        /// <param name="courseId">Ders ID</param>
        /// <param name="archivistEmail">Archivist email</param>
        /// <param name="academicianName">Akademisyen adı</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendPortfolioVerificationNotificationAsync(string courseId, string archivistEmail, string academicianName);

        /// <summary>
        /// Staff competency evaluation notification'ı gönder
        /// </summary>
        /// <param name="evaluationId">Değerlendirme ID</param>
        /// <param name="managerEmail">Manager email</param>
        /// <param name="staffName">Personel adı</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendStaffCompetencyEvaluationNotificationAsync(string evaluationId, string managerEmail, string staffName);

        /// <summary>
        /// Department performance data entry notification'ı gönder
        /// </summary>
        /// <param name="departmentId">Bölüm ID</param>
        /// <param name="strategicOfficeEmail">Stratejik ofis email</param>
        /// <param name="period">Dönem</param>
        /// <returns>Gönderim başarılı mı?</returns>
        Task<bool> SendDepartmentPerformanceNotificationAsync(string departmentId, string strategicOfficeEmail, string period);

        #endregion

        #region Health Check

        /// <summary>
        /// SMTP bağlantısını test et
        /// </summary>
        /// <returns>Bağlantı başarılı mı?</returns>
        Task<bool> TestSmtpConnectionAsync();

        /// <summary>
        /// Test email gönder
        /// </summary>
        /// <param name="testEmail">Test email adresi</param>
        /// <returns>Test başarılı mı?</returns>
        Task<bool> SendTestEmailAsync(string testEmail);

        #endregion
    }
}
