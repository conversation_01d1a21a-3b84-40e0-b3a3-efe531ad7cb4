using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Feedback filtreleme için Co sınıfı
    /// </summary>
    public class GetFeedbackFilterCo
    {
        /// <summary>
        /// Submission ID filtresi
        /// </summary>
        public string? SubmissionId { get; set; }

        /// <summary>
        /// Feedback türü filtresi
        /// </summary>
        public string? FeedbackType { get; set; }

        /// <summary>
        /// Status filtresi
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Başlangıç tarihi filtresi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi filtresi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Akademisyen kullanıcı ID filtresi
        /// </summary>
        public string? AcademicianUserId { get; set; }

        /// <summary>
        /// Controller kullanı<PERSON><PERSON> ID filtresi
        /// </summary>
        public string? ControllerUserId { get; set; }

        /// <summary>
        /// Form ID filtresi
        /// </summary>
        public string? FormId { get; set; }

        /// <summary>
        /// Kriter ID filtresi
        /// </summary>
        public string? CriterionId { get; set; }

        /// <summary>
        /// Minimum rating filtresi
        /// </summary>
        public int? MinRating { get; set; }

        /// <summary>
        /// Maximum rating filtresi
        /// </summary>
        public int? MaxRating { get; set; }

        /// <summary>
        /// Sadece revizyon gerektiren feedback'ler
        /// </summary>
        public bool? RequiresRevisionOnly { get; set; }

        /// <summary>
        /// Sadece deadline yaklaşan feedback'ler
        /// </summary>
        public bool? UpcomingDeadlineOnly { get; set; }

        /// <summary>
        /// Deadline threshold (gün)
        /// </summary>
        public int? DeadlineThresholdDays { get; set; }

        /// <summary>
        /// Sadece aktif feedback'ler
        /// </summary>
        public bool? ActiveOnly { get; set; } = true;
    }
}
