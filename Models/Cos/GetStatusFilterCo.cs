namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Status-based filtering için criteria object
    /// </summary>
    public class GetStatusFilterCo
    {
        /// <summary>
        /// Ek filtreleme için arama metni
        /// </summary>
        public string? NameContains { get; set; }

        /// <summary>
        /// Sadece aktif kayıtları getir
        /// </summary>
        public bool? OnlyActive { get; set; }

        /// <summary>
        /// Oluşturulma tarihi başlangıcı
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// Oluşturulma tarihi bitişi
        /// </summary>
        public DateTime? CreatedBefore { get; set; }
    }
}
