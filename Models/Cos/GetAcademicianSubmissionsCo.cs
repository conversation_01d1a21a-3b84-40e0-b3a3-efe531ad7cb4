namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Akademisyenin submission'larını getirmek için criteria object
    /// </summary>
    public class GetAcademicianSubmissionsCo
    {
        /// <summary>
        /// Form adında arama yapılacak metin
        /// </summary>
        public string? FormNameContains { get; set; }

        /// <summary>
        /// Submission durumu (NotStarted, InProgress, Submitted, Approved)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Başlangıç tarihi (submission created date)
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi (submission created date)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Sadece tamamlanmış submission'ları getir
        /// </summary>
        public bool? OnlyCompleted { get; set; }
    }
}
