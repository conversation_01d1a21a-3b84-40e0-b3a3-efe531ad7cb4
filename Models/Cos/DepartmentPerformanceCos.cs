using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Bölüm performans filtreleme Co'su
    /// </summary>
    public class DepartmentPerformanceFilterCo
    {
        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        public string? DepartmentId { get; set; }

        /// <summary>
        /// Fakülte ID'si
        /// </summary>
        public string? FacultyId { get; set; }

        /// <summary>
        /// Değerlendirme dönemi
        /// </summary>
        public string? Period { get; set; }

        /// <summary>
        /// Başlangıç tarihi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Minimum genel skor
        /// </summary>
        [Range(0, 100)]
        public double? MinOverallScore { get; set; }

        /// <summary>
        /// Maksimum genel skor
        /// </summary>
        [Range(0, 100)]
        public double? MaxOverallScore { get; set; }

        /// <summary>
        /// Durum filtresi
        /// </summary>
        public string? Status { get; set; } // "Excellent", "Good", "Average", "Poor"

        /// <summary>
        /// Aktif kayıtlar
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Arama terimi (bölüm adı, fakülte adı)
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Sıralama alanı
        /// </summary>
        public string? SortBy { get; set; } = "OverallScore";

        /// <summary>
        /// Sıralama yönü
        /// </summary>
        public string? SortDirection { get; set; } = "desc"; // "asc", "desc"
    }

    /// <summary>
    /// Bölüm karşılaştırma Co'su
    /// </summary>
    public class DepartmentComparisonCo
    {
        /// <summary>
        /// Karşılaştırılacak bölüm ID'leri
        /// </summary>
        [Required]
        [MinLength(2)]
        public List<string> DepartmentIds { get; set; } = new();

        /// <summary>
        /// Karşılaştırma dönemi
        /// </summary>
        [Required]
        public string Period { get; set; } = string.Empty;

        /// <summary>
        /// Karşılaştırma metrikleri
        /// </summary>
        public List<string> Metrics { get; set; } = new()
        {
            "OverallScore",
            "AcademicStaffPerformance",
            "ResearchPerformance",
            "PublicationPerformance",
            "StudentSatisfactionScore"
        };

        /// <summary>
        /// Benchmark dahil et
        /// </summary>
        public bool IncludeBenchmark { get; set; } = true;
    }

    /// <summary>
    /// Bölüm trend analizi Co'su
    /// </summary>
    public class DepartmentTrendAnalysisCo
    {
        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        [Required]
        public string DepartmentId { get; set; } = string.Empty;

        /// <summary>
        /// Analiz dönemi sayısı
        /// </summary>
        [Range(3, 24)]
        public int PeriodCount { get; set; } = 12;

        /// <summary>
        /// Başlangıç dönemi
        /// </summary>
        public string? StartPeriod { get; set; }

        /// <summary>
        /// Bitiş dönemi
        /// </summary>
        public string? EndPeriod { get; set; }

        /// <summary>
        /// Analiz metrikleri
        /// </summary>
        public List<string> Metrics { get; set; } = new()
        {
            "OverallScore",
            "AcademicStaffPerformance",
            "ResearchPerformance",
            "PublicationPerformance"
        };

        /// <summary>
        /// Trend hesaplama yöntemi
        /// </summary>
        public string TrendCalculationMethod { get; set; } = "LinearRegression"; // "LinearRegression", "MovingAverage", "Simple"
    }

    /// <summary>
    /// Bölüm dashboard Co'su
    /// </summary>
    public class DepartmentDashboardCo
    {
        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        [Required]
        public string DepartmentId { get; set; } = string.Empty;

        /// <summary>
        /// Dashboard dönemi
        /// </summary>
        public string? Period { get; set; }

        /// <summary>
        /// Trend veri sayısı
        /// </summary>
        [Range(3, 12)]
        public int TrendDataCount { get; set; } = 6;

        /// <summary>
        /// Uyarıları dahil et
        /// </summary>
        public bool IncludeAlerts { get; set; } = true;

        /// <summary>
        /// İstatistikleri dahil et
        /// </summary>
        public bool IncludeStatistics { get; set; } = true;

        /// <summary>
        /// Sıralama bilgilerini dahil et
        /// </summary>
        public bool IncludeRanking { get; set; } = true;
    }

    /// <summary>
    /// Bölüm sıralama Co'su
    /// </summary>
    public class DepartmentRankingCo
    {
        /// <summary>
        /// Sıralama dönemi
        /// </summary>
        [Required]
        public string Period { get; set; } = string.Empty;

        /// <summary>
        /// Fakülte ID'si (opsiyonel filtreleme)
        /// </summary>
        public string? FacultyId { get; set; }

        /// <summary>
        /// Sıralama metriği
        /// </summary>
        public string RankingMetric { get; set; } = "OverallScore";

        /// <summary>
        /// Minimum bölüm sayısı
        /// </summary>
        [Range(1, int.MaxValue)]
        public int MinDepartmentCount { get; set; } = 1;

        /// <summary>
        /// Sadece aktif bölümler
        /// </summary>
        public bool ActiveOnly { get; set; } = true;
    }

    /// <summary>
    /// Bölüm benchmark Co'su
    /// </summary>
    public class DepartmentBenchmarkCo
    {
        /// <summary>
        /// Benchmark dönemi
        /// </summary>
        [Required]
        public string Period { get; set; } = string.Empty;

        /// <summary>
        /// Benchmark türü
        /// </summary>
        public string BenchmarkType { get; set; } = "All"; // "All", "Faculty", "Similar"

        /// <summary>
        /// Fakülte ID'si (Faculty benchmark için)
        /// </summary>
        public string? FacultyId { get; set; }

        /// <summary>
        /// Benzer bölüm kriterleri (Similar benchmark için)
        /// </summary>
        public DepartmentSimilarityCriteria? SimilarityCriteria { get; set; }

        /// <summary>
        /// İstatistik türleri
        /// </summary>
        public List<string> StatisticTypes { get; set; } = new()
        {
            "Average",
            "Median",
            "StandardDeviation",
            "TopPerformer",
            "BottomPerformer"
        };
    }

    /// <summary>
    /// Bölüm benzerlik kriterleri
    /// </summary>
    public class DepartmentSimilarityCriteria
    {
        /// <summary>
        /// Öğrenci sayısı aralığı
        /// </summary>
        public StudentCountRange? StudentCountRange { get; set; }

        /// <summary>
        /// Akademik personel sayısı aralığı
        /// </summary>
        public StaffCountRange? StaffCountRange { get; set; }

        /// <summary>
        /// Bölüm türü
        /// </summary>
        public string? DepartmentType { get; set; }

        /// <summary>
        /// Fakülte türü
        /// </summary>
        public string? FacultyType { get; set; }
    }

    /// <summary>
    /// Öğrenci sayısı aralığı
    /// </summary>
    public class StudentCountRange
    {
        public int MinCount { get; set; }
        public int MaxCount { get; set; }
    }

    /// <summary>
    /// Personel sayısı aralığı
    /// </summary>
    public class StaffCountRange
    {
        public int MinCount { get; set; }
        public int MaxCount { get; set; }
    }

    /// <summary>
    /// Bölüm performans rapor Co'su
    /// </summary>
    public class DepartmentPerformanceReportCo
    {
        /// <summary>
        /// Rapor türü
        /// </summary>
        [Required]
        public string ReportType { get; set; } = string.Empty; // "Summary", "Detailed", "Comparison", "Trend"

        /// <summary>
        /// Bölüm ID'leri
        /// </summary>
        public List<string> DepartmentIds { get; set; } = new();

        /// <summary>
        /// Fakülte ID'si
        /// </summary>
        public string? FacultyId { get; set; }

        /// <summary>
        /// Rapor dönemi
        /// </summary>
        [Required]
        public string Period { get; set; } = string.Empty;

        /// <summary>
        /// Rapor formatı
        /// </summary>
        public string ReportFormat { get; set; } = "JSON"; // "JSON", "PDF", "Excel"

        /// <summary>
        /// Dahil edilecek bölümler
        /// </summary>
        public List<string> IncludeSections { get; set; } = new()
        {
            "Summary",
            "Performance",
            "Trends",
            "Ranking",
            "Recommendations"
        };

        /// <summary>
        /// Detay seviyesi
        /// </summary>
        public string DetailLevel { get; set; } = "Standard"; // "Basic", "Standard", "Detailed"
    }
}
