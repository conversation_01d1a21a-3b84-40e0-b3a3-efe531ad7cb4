namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Değerlendirme formları için arama kriterleri
    /// </summary>
    public class GetEvaluationFormsCo
    {
        /// <summary>
        /// Form adında arama yapılacak metin
        /// </summary>
        public string? NameContains { get; set; }

        /// <summary>
        /// Form durumu (Draft, Active, Archived)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Oluşturan kullanıcı ID'si
        /// </summary>
        public string? CreatedByUserId { get; set; }

        /// <summary>
        /// Başlangıç tarihi (CreatedAt >= bu tarih)
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// Bitiş tarihi (CreatedAt <= bu tarih)
        /// </summary>
        public DateTime? CreatedBefore { get; set; }

        /// <summary>
        /// Değerlendirme periyodu ba<PERSON><PERSON><PERSON><PERSON> tarihi (EvaluationPeriodStartDate >= bu tarih)
        /// </summary>
        public DateTime? EvaluationPeriodAfter { get; set; }

        /// <summary>
        /// Değerlendirme periyodu bitiş tarihi (EvaluationPeriodEndDate <= bu tarih)
        /// </summary>
        public DateTime? EvaluationPeriodBefore { get; set; }

        /// <summary>
        /// Teslim tarihi yaklaşan formlar (SubmissionDeadline <= şu andan itibaren X gün)
        /// </summary>
        public int? DeadlineWithinDays { get; set; }

        /// <summary>
        /// Sadece aktif formları getir
        /// </summary>
        public bool? OnlyActive { get; set; }

        /// <summary>
        /// Sadece taslak formları getir
        /// </summary>
        public bool? OnlyDrafts { get; set; }
    }
}
