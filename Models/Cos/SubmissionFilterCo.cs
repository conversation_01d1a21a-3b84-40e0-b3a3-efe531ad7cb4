using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Submission filtreleme için kullan<PERSON>lan <PERSON> (Command Object)
    /// Controller dashboard'unda submission listelerini filtrelemek için kullanılır
    /// </summary>
    public class SubmissionFilterCo
    {
        /// <summary>
        /// Status'a göre filtreleme (Submitted, UnderReview, Approved, Rejected)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Akademisyen adına göre filtreleme
        /// </summary>
        public string? AcademicianName { get; set; }

        /// <summary>
        /// Bölüme göre filtreleme
        /// </summary>
        public string? Department { get; set; }

        /// <summary>
        /// Akademik kadroya göre filtreleme
        /// </summary>
        public string? AcademicCadre { get; set; }

        /// <summary>
        /// Form adına göre filtreleme
        /// </summary>
        public string? FormName { get; set; }

        /// <summary>
        /// Form ID'sine göre filtreleme
        /// </summary>
        public string? FormId { get; set; }

        /// <summary>
        /// Başlangıç tarihi (submission date)
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi (submission date)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Deadline yaklaşan submission'ları getir (gün sayısı)
        /// </summary>
        public int? DeadlineWithinDays { get; set; }

        /// <summary>
        /// Öncelik seviyesine göre filtreleme (High, Medium, Low)
        /// </summary>
        public string? Priority { get; set; }

        /// <summary>
        /// Evidence file sayısına göre minimum filtreleme
        /// </summary>
        public int? MinEvidenceFileCount { get; set; }

        /// <summary>
        /// Evidence file sayısına göre maksimum filtreleme
        /// </summary>
        public int? MaxEvidenceFileCount { get; set; }

        /// <summary>
        /// Atanmış controller ID'sine göre filtreleme
        /// </summary>
        public string? AssignedControllerId { get; set; }

        /// <summary>
        /// Sadece atanmamış submission'ları getir
        /// </summary>
        public bool? UnassignedOnly { get; set; }

        /// <summary>
        /// Sadece review edilmemiş submission'ları getir
        /// </summary>
        public bool? UnreviewedOnly { get; set; }

        /// <summary>
        /// Genel arama terimi (academician name, form name, department vb.)
        /// </summary>
        public string? SearchTerm { get; set; }
    }

    /// <summary>
    /// Submission sıralama seçenekleri
    /// </summary>
    public static class SubmissionSortOptions
    {
        public const string SubmittedAt = "SubmittedAt";
        public const string AcademicianName = "AcademicianName";
        public const string FormName = "FormName";
        public const string Department = "Department";
        public const string Status = "Status";
        public const string Deadline = "Deadline";
        public const string Priority = "Priority";
        public const string EvidenceFileCount = "EvidenceFileCount";
    }

    /// <summary>
    /// Sıralama yönü
    /// </summary>
    public static class SortDirection
    {
        public const string Ascending = "asc";
        public const string Descending = "desc";
    }
}
