using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Cos
{
    /// <summary>
    /// Generic Data Entry filtreleme için Co sınıfı
    /// </summary>
    public class GenericDataEntryFilterCo
    {
        /// <summary>
        /// Definition ID filtresi
        /// </summary>
        public string? DefinitionId { get; set; }

        /// <summary>
        /// Definition adı filtresi
        /// </summary>
        public string? DefinitionName { get; set; }

        /// <summary>
        /// Kategori filtresi
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Status filtresi
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Başlangıç tarihi filtresi
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi filtresi
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Oluşturan kullanıcı ID filtresi
        /// </summary>
        public string? CreatedByUserId { get; set; }

        /// <summary>
        /// Sadece aktif kayıtlar
        /// </summary>
        public bool? ActiveOnly { get; set; } = true;

        /// <summary>
        /// Arama terimi (definition name, description, category'de arar)
        /// </summary>
        public string? SearchTerm { get; set; }
    }
}
