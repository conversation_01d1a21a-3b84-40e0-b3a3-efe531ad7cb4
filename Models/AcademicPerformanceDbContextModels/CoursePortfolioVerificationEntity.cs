using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

/// <summary>
/// Ders bazlı portfolio verification entity'si
/// 11 portfolio başlığı için verification durumlarını takip eder
/// Portfolio kontrol modülü için kullanılır
/// </summary>
public class CoursePortfolioVerificationEntity : EntityBaseModel
{
    /// <summary>
    /// Akademisyen TC Kimlik Numarası
    /// </summary>
    [Required]
    [StringLength(11)]
    public required string AcademicianTc { get; set; }

    /// <summary>
    /// Ders ID'si
    /// </summary>
    [Required]
    public required string CourseId { get; set; }

    /// <summary>
    /// Öğrenci ID'si (opsiyonel)
    /// </summary>
    public string? StudentId { get; set; }

    /// <summary>
    /// Ders kodu (örn: BIL101, MAT201)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string CourseCode { get; set; }

    /// <summary>
    /// Dönem adı (örn: 2024-2025-Güz, 2024-2025-Bahar)
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string PeriodName { get; set; }

    /// <summary>
    /// Ders adı (örn: Bilgisayar Programlama, Matematik I)
    /// </summary>
    [Required]
    [StringLength(200)]
    public required string CourseName { get; set; }

    // 11 Portfolio Başlığı için Verification Status'ları
    // Her biri için: Pending, VerifiedInEbys, Missing, DiscrepancyFound, NotApplicable

    /// <summary>
    /// Sınav kâğıtları verification durumu
    /// </summary>
    [StringLength(50)]
    public string ExamPapersStatus { get; set; } = "Pending";

    /// <summary>
    /// Yanıt anahtarı verification durumu
    /// </summary>
    [StringLength(50)]
    public string AnswerKeyStatus { get; set; } = "Pending";

    /// <summary>
    /// Sınav tutanağı verification durumu
    /// </summary>
    [StringLength(50)]
    public string ExamRecordStatus { get; set; } = "Pending";

    /// <summary>
    /// Yoklama kâğıdı verification durumu
    /// </summary>
    [StringLength(50)]
    public string AttendanceSheetStatus { get; set; } = "Pending";

    /// <summary>
    /// Ders izlencesi verification durumu
    /// </summary>
    [StringLength(50)]
    public string CourseSyllabusStatus { get; set; } = "Pending";

    /// <summary>
    /// 14 haftalık ders yoklama listeleri verification durumu
    /// </summary>
    [StringLength(50)]
    public string WeeklyAttendanceStatus { get; set; } = "Pending";

    /// <summary>
    /// Bütünleme sınavından alınmış not çizelgesi verification durumu
    /// </summary>
    [StringLength(50)]
    public string MakeupExamGradesStatus { get; set; } = "Pending";

    /// <summary>
    /// UZEM Sistemi'nde ders ve sınav yoklamaları verification durumu
    /// </summary>
    [StringLength(50)]
    public string UzemRecordsStatus { get; set; } = "Pending";

    /// <summary>
    /// Genel verification durumu
    /// </summary>
    [StringLength(50)]
    public string VerificationStatus { get; set; } = "Pending";

    /// <summary>
    /// Genel verification notları
    /// </summary>
    [StringLength(2000)]
    public string? VerificationNotes { get; set; }

    /// <summary>
    /// Son verification tarihi
    /// </summary>
    public DateTime? LastVerifiedAt { get; set; }

    /// <summary>
    /// Son verification yapan archivist kullanıcı ID'si
    /// </summary>
    [StringLength(256)]
    public string? LastVerifiedByArchivistId { get; set; }

    /// <summary>
    /// EBYS referans ID'si
    /// </summary>
    [StringLength(200)]
    public string? EbysReferenceId { get; set; }

    /// <summary>
    /// Audit fields - Oluşturulma tarihi
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Audit fields - Oluşturan kullanıcı ID'si
    /// </summary>
    [StringLength(256)]
    public string? CreatedByUserId { get; set; }

    /// <summary>
    /// Audit fields - Güncellenme tarihi
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Audit fields - Güncelleyen kullanıcı ID'si
    /// </summary>
    [StringLength(256)]
    public string? UpdatedByUserId { get; set; }

    // Navigation properties

    /// <summary>
    /// Akademisyen profili navigation property
    /// </summary>
    public virtual AcademicianProfileEntity? AcademicianProfile { get; set; }

    /// <summary>
    /// Genel verification durumunu hesaplar
    /// </summary>
    public string GetOverallStatus()
    {
        var statuses = new[]
        {
            ExamPapersStatus,
            AnswerKeyStatus,
            ExamRecordStatus,
            AttendanceSheetStatus,
            CourseSyllabusStatus,
            WeeklyAttendanceStatus,
            MakeupExamGradesStatus,
            UzemRecordsStatus
        };

        if (statuses.All(s => s == "VerifiedInEbys"))
            return "AllVerified";

        if (statuses.Any(s => s == "Missing" || s == "DiscrepancyFound"))
            return "HasIssues";

        if (statuses.Any(s => s == "VerifiedInEbys"))
            return "PartiallyVerified";

        if (statuses.All(s => s == "NotApplicable"))
            return "NotApplicable";

        return "Pending";
    }

    /// <summary>
    /// Verification tamamlanma yüzdesini hesaplar
    /// </summary>
    public double GetCompletionPercentage()
    {
        var statuses = new[]
        {
            ExamPapersStatus,
            AnswerKeyStatus,
            ExamRecordStatus,
            AttendanceSheetStatus,
            CourseSyllabusStatus,
            WeeklyAttendanceStatus,
            MakeupExamGradesStatus,
            UzemRecordsStatus
        };

        var completedCount = statuses.Count(s => s == "VerifiedInEbys" || s == "NotApplicable");
        return (double)completedCount / statuses.Length * 100;
    }

    /// <summary>
    /// Bekleyen verification sayısını döndürür
    /// </summary>
    public int GetPendingCount()
    {
        var statuses = new[]
        {
            ExamPapersStatus,
            AnswerKeyStatus,
            ExamRecordStatus,
            AttendanceSheetStatus,
            CourseSyllabusStatus,
            WeeklyAttendanceStatus,
            MakeupExamGradesStatus,
            UzemRecordsStatus
        };

        return statuses.Count(s => s == "Pending");
    }

    /// <summary>
    /// Sorunlu verification sayısını döndürür
    /// </summary>
    public int GetIssueCount()
    {
        var statuses = new[]
        {
            ExamPapersStatus,
            AnswerKeyStatus,
            ExamRecordStatus,
            AttendanceSheetStatus,
            CourseSyllabusStatus,
            WeeklyAttendanceStatus,
            MakeupExamGradesStatus,
            UzemRecordsStatus
        };

        return statuses.Count(s => s == "Missing" || s == "DiscrepancyFound");
    }
}
