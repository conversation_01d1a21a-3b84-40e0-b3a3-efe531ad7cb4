using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class GenericDataEntryRecordEntity : EntityBaseModel
{
    [Required]
    public required string DefinitionId { get; set; }

    [Required]
    public required string RecordData { get; set; }

    [StringLength(50)]
    public string Status { get; set; } = "Draft";

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public string? CreatedByUserId { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string? UpdatedByUserId { get; set; }

    // Navigation properties
    public virtual GenericDataEntryDefinitionEntity? Definition { get; set; }
}
