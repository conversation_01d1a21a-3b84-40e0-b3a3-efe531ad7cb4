using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class EvaluationFormEntity : EntityBaseModel
{
    public required string Name { get; set; }
    public string? Description { get; set; }
    public string? ApplicableAcademicCadresJson { get; set; } // Store as JSON string "[ \"Prof\", \"AssocProf\" ]"
    public DateTime EvaluationPeriodStartDate { get; set; }
    public DateTime EvaluationPeriodEndDate { get; set; }
    public DateTime? SubmissionDeadline { get; set; }
    public required string Status { get; set; } // Draft, Active, Archived
    public string? CreatedByUserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? UpdatedByUserId { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public virtual ICollection<FormCategoryEntity>? Categories { get; set; }
    public virtual ICollection<AcademicSubmissionEntity>? Submissions { get; set; }
}
