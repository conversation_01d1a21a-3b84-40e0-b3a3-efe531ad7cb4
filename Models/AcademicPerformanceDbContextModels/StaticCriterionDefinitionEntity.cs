using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class StaticCriterionDefinitionEntity
{
    [Key]
    [StringLength(100)]
    public required string StaticCriterionSystemId { get; set; } // e.g., "PUBLICATION_COUNT"

    [Required]
    [StringLength(200)]
    public required string Name { get; set; }

    [StringLength(1000)]
    public string? Description { get; set; }

    [Required]
    [StringLength(50)]
    public required string DataType { get; set; } // Inte<PERSON>, Decimal, Boolean, String, Date

    [StringLength(500)]
    public string? DataSourceHint { get; set; }

    [StringLength(2000)]
    public string? CalculationLogic { get; set; }

    public bool IsActive { get; set; } = true;

    public virtual ICollection<FormCriterionLinkEntity>? FormCriterionLinks { get; set; }
}
