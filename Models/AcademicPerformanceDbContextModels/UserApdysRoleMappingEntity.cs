using Rlx.Shared.Models;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class UserApdysRoleMappingEntity : EntityBaseIdModel
{
    public required string UniversityUserId { get; set; }
    public int ApdysRoleAutoIncrementId { get; set; }
    public DateTime AssignedAt { get; set; }
    public string? AssignedByUserId { get; set; }
    public DateTime? RevokedAt { get; set; }
    public string? RevokedByUserId { get; set; }
    public virtual ApdysRoleEntity? Role { get; set; }
}
