using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class DepartmentStrategicPerformanceDataEntity : EntityBaseModel
{
    [Required]
    [StringLength(100)]
    public required string DepartmentId { get; set; }

    [Required]
    [StringLength(100)]
    public required string AssessmentPeriodId { get; set; }

    [Required]
    [StringLength(100)]
    public required string IndicatorSystemId { get; set; } // FK

    public string? ActualValue { get; set; }
    public string? TargetValue { get; set; }

    [StringLength(2000)]
    public string? Notes { get; set; }

    public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;

    [Required]
    public required string SubmittedByUserId { get; set; }

    // Navigation properties
    public virtual DepartmentStrategicIndicatorDefinitionEntity? IndicatorDefinition { get; set; }
}
