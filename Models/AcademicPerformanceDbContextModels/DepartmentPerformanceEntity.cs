using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels
{
    /// <summary>
    /// Bölüm performans entity'si
    /// </summary>
    [Table("DepartmentPerformances")]
    public class DepartmentPerformanceEntity : EntityBaseModel
    {
        /// <summary>
        /// Bölüm ID'si
        /// </summary>
        [Required]
        [StringLength(255)]
        public required string DepartmentId { get; set; }

        /// <summary>
        /// Bölüm adı (cache için)
        /// </summary>
        [StringLength(500)]
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Fakülte ID'si
        /// </summary>
        [Required]
        [StringLength(255)]
        public required string FacultyId { get; set; }

        /// <summary>
        /// Fakülte adı (cache için)
        /// </summary>
        [StringLength(500)]
        public string? FacultyName { get; set; }

        /// <summary>
        /// Değerlendirme dönemi
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string Period { get; set; }

        /// <summary>
        /// Değerlendirme tarihi
        /// </summary>
        [Required]
        public DateTime EvaluationDate { get; set; }

        #region Performance Metrics

        /// <summary>
        /// Genel performans skoru (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal OverallScore { get; set; }

        /// <summary>
        /// Akademik personel performansı (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal AcademicStaffPerformance { get; set; }

        /// <summary>
        /// Araştırma performansı (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal ResearchPerformance { get; set; }

        /// <summary>
        /// Yayın performansı (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal PublicationPerformance { get; set; }

        /// <summary>
        /// Öğrenci memnuniyet skoru (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal StudentSatisfactionScore { get; set; }

        /// <summary>
        /// Altyapı skoru (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal InfrastructureScore { get; set; }

        /// <summary>
        /// Bütçe etkinlik skoru (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal BudgetEfficiencyScore { get; set; }

        #endregion

        #region Statistics

        /// <summary>
        /// Toplam akademik personel sayısı
        /// </summary>
        [Range(0, int.MaxValue)]
        public int TotalAcademicStaff { get; set; }

        /// <summary>
        /// Toplam öğrenci sayısı
        /// </summary>
        [Range(0, int.MaxValue)]
        public int TotalStudents { get; set; }

        /// <summary>
        /// Tamamlanan gönderim sayısı
        /// </summary>
        [Range(0, int.MaxValue)]
        public int CompletedSubmissions { get; set; }

        /// <summary>
        /// Bekleyen gönderim sayısı
        /// </summary>
        [Range(0, int.MaxValue)]
        public int PendingSubmissions { get; set; }

        /// <summary>
        /// Tamamlanma oranı (0-100)
        /// </summary>
        [Range(0, 100)]
        [Column(TypeName = "decimal(5,2)")]
        public decimal CompletionRate { get; set; }

        #endregion

        #region Ranking & Status

        /// <summary>
        /// Bölüm sıralaması (dönem içinde)
        /// </summary>
        public int? Ranking { get; set; }

        /// <summary>
        /// Toplam bölüm sayısı (sıralama için)
        /// </summary>
        public int? TotalDepartmentsInRanking { get; set; }

        /// <summary>
        /// Performans durumu
        /// </summary>
        [StringLength(50)]
        public string? Status { get; set; } // "Excellent", "Good", "Average", "Poor"

        /// <summary>
        /// Trend durumu
        /// </summary>
        [StringLength(50)]
        public string? Trend { get; set; } // "Improving", "Declining", "Stable"

        #endregion

        #region Additional Data

        /// <summary>
        /// Notlar ve açıklamalar
        /// </summary>
        [StringLength(2000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Ek veriler (JSON format)
        /// </summary>
        [Column(TypeName = "jsonb")]
        public string? AdditionalData { get; set; }

        /// <summary>
        /// Hesaplama detayları (JSON format)
        /// </summary>
        [Column(TypeName = "jsonb")]
        public string? CalculationDetails { get; set; }

        #endregion

        #region Audit Fields

        /// <summary>
        /// Oluşturan kullanıcı ID'si
        /// </summary>
        [Required]
        [StringLength(255)]
        public required string CreatedByUserId { get; set; }

        /// <summary>
        /// Oluşturan kullanıcı adı (cache için)
        /// </summary>
        [StringLength(500)]
        public string? CreatedByUserName { get; set; }

        /// <summary>
        /// Güncelleyen kullanıcı ID'si
        /// </summary>
        [StringLength(255)]
        public string? UpdatedByUserId { get; set; }

        /// <summary>
        /// Güncelleyen kullanıcı adı (cache için)
        /// </summary>
        [StringLength(500)]
        public string? UpdatedByUserName { get; set; }

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Onaylayan kullanıcı ID'si
        /// </summary>
        [StringLength(255)]
        public string? ApprovedByUserId { get; set; }

        /// <summary>
        /// Onay tarihi
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Onay durumu
        /// </summary>
        public bool IsApproved { get; set; } = false;

        /// <summary>
        /// Kayıt oluşturulma tarihi
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Aktif durumu
        /// </summary>
        public bool IsActive { get; set; } = true;

        #endregion

        #region Indexes and Constraints

        // Unique constraint: DepartmentId + Period
        // Index: DepartmentId, FacultyId, Period, EvaluationDate
        // Index: OverallScore, Ranking
        // Index: CreatedByUserId, CreatedAt

        #endregion

        #region Navigation Properties

        // Navigation properties can be added here if needed
        // For example, relationships to Department, Faculty entities

        #endregion

        #region Calculated Properties

        /// <summary>
        /// Performans kategorisi (hesaplanmış)
        /// </summary>
        [NotMapped]
        public string PerformanceCategory
        {
            get
            {
                return OverallScore switch
                {
                    >= 90 => "Excellent",
                    >= 75 => "Good",
                    >= 60 => "Average",
                    _ => "Poor"
                };
            }
        }

        /// <summary>
        /// Toplam gönderim sayısı (hesaplanmış)
        /// </summary>
        [NotMapped]
        public int TotalSubmissions => CompletedSubmissions + PendingSubmissions;

        /// <summary>
        /// Sıralama yüzdesi (hesaplanmış)
        /// </summary>
        [NotMapped]
        public decimal? RankingPercentile
        {
            get
            {
                if (Ranking.HasValue && TotalDepartmentsInRanking.HasValue && TotalDepartmentsInRanking > 0)
                {
                    return Math.Round((decimal)(TotalDepartmentsInRanking.Value - Ranking.Value + 1) / TotalDepartmentsInRanking.Value * 100, 2);
                }
                return null;
            }
        }

        #endregion
    }
}
