using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

/// <summary>
/// Feedback entry entity - ReportingStore için genel feedback kayıtları
/// SubmissionFeedbackEntity'den farklı olarak reporting ve analytics için optimize edilmiş
/// </summary>
public class FeedbackEntryEntity : EntityBaseModel
{
    /// <summary>
    /// İlgili submission ID'si
    /// </summary>
    [Required]
    public required string SubmissionId { get; set; }

    /// <summary>
    /// Feedback türü (Approval, Rejection, RevisionRequest, Comment)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string FeedbackType { get; set; }

    /// <summary>
    /// Feedback veren kullanıcı ID'si
    /// </summary>
    [Required]
    public required string CreatedBy { get; set; }

    /// <summary>
    /// Feedback mesajı/yorumu
    /// </summary>
    [StringLength(2000)]
    public string? Comments { get; set; }

    /// <summary>
    /// Feedback durumu (Active, Resolved, Superseded)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Status { get; set; } = "Active";

    /// <summary>
    /// Feedback önceliği (Low, Medium, High, Critical)
    /// </summary>
    [StringLength(20)]
    public string Priority { get; set; } = "Medium";

    /// <summary>
    /// Feedback kategorisi (Quality, Completeness, Accuracy, Compliance)
    /// </summary>
    [StringLength(50)]
    public string? Category { get; set; }

    /// <summary>
    /// Feedback puanı (1-10 arası, opsiyonel)
    /// </summary>
    public int? Rating { get; set; }

    /// <summary>
    /// Yanıt süresi (saat cinsinden)
    /// </summary>
    public double? ResponseTimeHours { get; set; }

    /// <summary>
    /// Feedback'in çözülme tarihi
    /// </summary>
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// Çözüm notu
    /// </summary>
    [StringLength(1000)]
    public string? ResolutionNote { get; set; }

    /// <summary>
    /// İlgili form ID'si (opsiyonel)
    /// </summary>
    public string? FormId { get; set; }

    /// <summary>
    /// İlgili kriter ID'si (opsiyonel)
    /// </summary>
    public string? CriterionId { get; set; }

    /// <summary>
    /// Akademisyen kullanıcı ID'si
    /// </summary>
    public string? AcademicianUserId { get; set; }

    /// <summary>
    /// Bölüm ID'si (reporting için)
    /// </summary>
    public string? DepartmentId { get; set; }

    /// <summary>
    /// Metadata (JSON format)
    /// </summary>
    public string? Metadata { get; set; }

    // Audit fields
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual AcademicSubmissionEntity? AcademicSubmission { get; set; }
    public virtual EvaluationFormEntity? EvaluationForm { get; set; }
}
