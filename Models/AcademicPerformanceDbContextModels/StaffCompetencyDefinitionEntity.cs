using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class StaffCompetencyDefinitionEntity
{
    [Key]
    [StringLength(100)]
    public required string CompetencySystemId { get; set; } // örn., "LEADERSHIP_SKILLS"

    [Required]
    [StringLength(250)]
    public required string Name { get; set; }

    [StringLength(1000)]
    public string? Description { get; set; }

    public string? RatingScaleJson { get; set; } // Rating scale tanımları için JSON string olarak sakla

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public string? CreatedByUserId { get; set; }

    // Navigation property'leri
    public virtual ICollection<CompetencyRatingEntity>? CompetencyRatings { get; set; }
}
