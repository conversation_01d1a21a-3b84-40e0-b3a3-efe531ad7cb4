using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class AcademicianProfileEntity : EntityBaseModel
{
    [Required]
    [StringLength(256)]
    public required string UniversityUserId { get; set; } // From OrganizationManagement API

    [Required]
    [StringLength(100)]
    public required string Name { get; set; }

    [Required]
    [StringLength(100)]
    public required string Surname { get; set; }

    [StringLength(200)]
    public string? FullName { get; set; } // Computed: Name + " " + Surname

    [StringLength(200)]
    public string? Department { get; set; } // From OrganizationManagement API

    [StringLength(100)]
    public string? DepartmentId { get; set; } // Department ID for relationships and reporting

    [StringLength(100)]
    public string? FacultyId { get; set; } // Faculty ID for hierarchical reporting

    [StringLength(100)]
    public string? AcademicCadre { get; set; } // Prof, AssocProf, AssistProf, etc.

    [StringLength(200)]
    public string? Email { get; set; }

    [StringLength(100)]
    public string? Title { get; set; } // Dr., Prof. Dr., etc.

    public DateTime LastSyncedAt { get; set; } = DateTime.UtcNow;

    public bool IsActive { get; set; } = true;

    [StringLength(500)]
    public string? SyncNotes { get; set; } // For debugging sync issues

    // Audit fields
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedByUserId { get; set; }
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public string? UpdatedByUserId { get; set; }

    // Navigation properties
    public virtual ICollection<AcademicSubmissionEntity>? Submissions { get; set; }
}
