using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class StaffCompetencyEvaluationEntity : EntityBaseModel
{
    [Required]
    public required string AcademicianUniveristyUserId { get; set; }

    /// <summary>
    /// Staff ID'si (backward compatibility)
    /// </summary>
    [Required]
    public required string StaffId { get; set; }

    /// <summary>
    /// Competency ID'si
    /// </summary>
    [Required]
    public required string CompetencyId { get; set; }

    [Required]
    public required string EvaluatingManagerUserId { get; set; }

    [Required]
    [StringLength(100)]
    public required string EvaluationContextId { get; set; }

    [StringLength(4000)]
    public string? OverallComments { get; set; }

    [StringLength(100)]
    public string? DepartmentId { get; set; }

    [StringLength(50)]
    public string Status { get; set; } = "Draft";

    [StringLength(100)]
    public string? EvaluatorUserId { get; set; }

    public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Evaluation skoru (0-100 arası, opsiyonel)
    /// </summary>
    public decimal? Score { get; set; }

    // Navigation properties
    public virtual ICollection<CompetencyRatingEntity>? CompetencyRatings { get; set; } = new List<CompetencyRatingEntity>();
}
