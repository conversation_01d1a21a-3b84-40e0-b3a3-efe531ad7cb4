using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace AcademicPerformance.Models.MongoDocuments
{
    /// <summary>
    /// Ana submission document - Akademisyenin bir form için yaptığı değerlendirme süreci
    /// </summary>
    public class AcademicSubmissionDocument
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = ObjectId.GenerateNewId().ToString();

        /// <summary>
        /// Değerlendirme form ID'si
        /// </summary>
        [BsonElement("formId")]
        public string FormId { get; set; } = string.Empty;

        /// <summary>
        /// Akademisyen kullanıcı ID'si
        /// </summary>
        [BsonElement("academicianUserId")]
        public string AcademicianUserId { get; set; } = string.Empty;

        /// <summary>
        /// Submission durumu: Draft, InProgress, Submitted, UnderReview
        /// </summary>
        [BsonElement("status")]
        public string Status { get; set; } = "Draft";

        /// <summary>
        /// Oluşturulma tarihi
        /// </summary>
        [BsonElement("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        [BsonElement("updatedAt")]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Submit edilme tarihi
        /// </summary>
        [BsonElement("submittedAt")]
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Kriter verileri listesi
        /// </summary>
        [BsonElement("criteriaData")]
        public List<SubmissionCriterionData> CriteriaData { get; set; } = new();

        /// <summary>
        /// Tamamlanma yüzdesi (0-100)
        /// </summary>
        [BsonElement("completionPercentage")]
        public decimal CompletionPercentage { get; set; } = 0;

        /// <summary>
        /// Genel notlar
        /// </summary>
        [BsonElement("notes")]
        public string? Notes { get; set; }

        /// <summary>
        /// Son aktivite tarihi
        /// </summary>
        [BsonElement("lastActivityAt")]
        public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Kriter data - Her kriter için girilen veriler
    /// </summary>
    public class SubmissionCriterionData
    {
        /// <summary>
        /// Form kriter link ID'si
        /// </summary>
        [BsonElement("criterionLinkId")]
        public string CriterionLinkId { get; set; } = string.Empty;

        /// <summary>
        /// Kriter tipi: Dynamic veya Static
        /// </summary>
        [BsonElement("criterionType")]
        public string CriterionType { get; set; } = string.Empty;

        /// <summary>
        /// Kriter adı (cache için)
        /// </summary>
        [BsonElement("criterionName")]
        public string CriterionName { get; set; } = string.Empty;

        /// <summary>
        /// Veri girişleri listesi
        /// </summary>
        [BsonElement("dataEntries")]
        public List<CriterionDataEntry> DataEntries { get; set; } = new();

        /// <summary>
        /// Bu kriter tamamlandı mı?
        /// </summary>
        [BsonElement("isCompleted")]
        public bool IsCompleted { get; set; } = false;

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        [BsonElement("lastUpdated")]
        public DateTime? LastUpdated { get; set; }

        /// <summary>
        /// Kriter notları
        /// </summary>
        [BsonElement("notes")]
        public string? Notes { get; set; }

        /// <summary>
        /// Hesaplanan puan (varsa)
        /// </summary>
        [BsonElement("calculatedScore")]
        public decimal? CalculatedScore { get; set; }
    }

    /// <summary>
    /// Data entry - Her input field için girilen veri
    /// </summary>
    public class CriterionDataEntry
    {
        /// <summary>
        /// Data entry ID'si
        /// </summary>
        [BsonElement("id")]
        public string Id { get; set; } = ObjectId.GenerateNewId().ToString();

        /// <summary>
        /// Field adı (örn: "PublicationTitle", "CourseCode")
        /// </summary>
        [BsonElement("fieldName")]
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// Field tipi: Text, Number, Date, File, Boolean, etc.
        /// </summary>
        [BsonElement("fieldType")]
        public string FieldType { get; set; } = string.Empty;

        /// <summary>
        /// Girilen değer
        /// </summary>
        [BsonElement("value")]
        public string? Value { get; set; }

        /// <summary>
        /// Açıklama/detay
        /// </summary>
        [BsonElement("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Oluşturulma tarihi
        /// </summary>
        [BsonElement("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Güncelleme tarihi
        /// </summary>
        [BsonElement("updatedAt")]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Dosya ID'si (file upload için)
        /// </summary>
        [BsonElement("fileId")]
        public string? FileId { get; set; }

        /// <summary>
        /// Validation durumu
        /// </summary>
        [BsonElement("isValid")]
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// Validation hata mesajı
        /// </summary>
        [BsonElement("validationError")]
        public string? ValidationError { get; set; }
    }
}
