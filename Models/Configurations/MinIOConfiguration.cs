using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Configurations;

/// <summary>
/// MinIO object storage konfigürasyon modeli
/// </summary>
public class MinIOConfiguration
{
    /// <summary>
    /// MinIO server endpoint (örn: localhost:9000)
    /// </summary>
    [Required]
    public string Endpoint { get; set; } = string.Empty;

    /// <summary>
    /// MinIO access key
    /// </summary>
    [Required]
    public string AccessKey { get; set; } = string.Empty;

    /// <summary>
    /// MinIO secret key
    /// </summary>
    [Required]
    public string SecretKey { get; set; } = string.Empty;

    /// <summary>
    /// SSL kullanımı (production'da true olmalı)
    /// </summary>
    public bool UseSSL { get; set; } = false;

    /// <summary>
    /// Varsayılan bucket adı
    /// </summary>
    [Required]
    public string DefaultBucket { get; set; } = "apdys-evidence-files";

    /// <summary>
    /// Maksimum dosya boyutu (bytes) - varsay<PERSON>lan 10MB
    /// </summary>
    [Range(1, long.MaxValue)]
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB

    /// <summary>
    /// İzin verilen dosya uzantıları
    /// </summary>
    public List<string> AllowedExtensions { get; set; } = new()
    {
        ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"
    };

    /// <summary>
    /// İzin verilen MIME türleri
    /// </summary>
    public List<string> AllowedMimeTypes { get; set; } = new()
    {
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "image/jpeg",
        "image/png"
    };

    /// <summary>
    /// Presigned URL geçerlilik süresi (saniye) - varsayılan 1 saat
    /// </summary>
    [Range(60, 86400)] // 1 dakika ile 24 saat arası
    public int PresignedUrlExpirySeconds { get; set; } = 3600; // 1 saat

    /// <summary>
    /// Bucket region (opsiyonel)
    /// </summary>
    public string? Region { get; set; }

    /// <summary>
    /// Connection timeout (saniye)
    /// </summary>
    [Range(1, 300)]
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Konfigürasyonun geçerli olup olmadığını kontrol eder
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(Endpoint) &&
               !string.IsNullOrWhiteSpace(AccessKey) &&
               !string.IsNullOrWhiteSpace(SecretKey) &&
               !string.IsNullOrWhiteSpace(DefaultBucket) &&
               MaxFileSize > 0 &&
               AllowedExtensions.Count > 0 &&
               AllowedMimeTypes.Count > 0;
    }

    /// <summary>
    /// Dosya uzantısının izin verilen listede olup olmadığını kontrol eder
    /// </summary>
    public bool IsExtensionAllowed(string extension)
    {
        return AllowedExtensions.Contains(extension.ToLowerInvariant());
    }

    /// <summary>
    /// MIME türünün izin verilen listede olup olmadığını kontrol eder
    /// </summary>
    public bool IsMimeTypeAllowed(string mimeType)
    {
        return AllowedMimeTypes.Contains(mimeType.ToLowerInvariant());
    }
}
