using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Akademisyen performans raporu DTO'su
    /// </summary>
    public class PerformanceReportDto
    {
        /// <summary>
        /// Rapor ID'si
        /// </summary>
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// Akademisyen bilgileri
        /// </summary>
        public AcademicianSummaryDto Academician { get; set; } = new();

        /// <summary>
        /// Rapor dönemi
        /// </summary>
        public ReportPeriodDto Period { get; set; } = new();

        /// <summary>
        /// Genel performans skoru (0-100)
        /// </summary>
        public double OverallScore { get; set; }

        /// <summary>
        /// Performans seviyesi (Excellent, Good, Average, Poor)
        /// </summary>
        public string PerformanceLevel { get; set; } = string.Empty;

        /// <summary>
        /// Kategori bazında performans skorları
        /// </summary>
        public List<CategoryPerformanceDto> CategoryScores { get; set; } = new();

        /// <summary>
        /// Kriter bazında detay skorlar
        /// </summary>
        public List<CriterionPerformanceDto> CriterionScores { get; set; } = new();

        /// <summary>
        /// Tamamlanan form sayısı
        /// </summary>
        public int CompletedForms { get; set; }

        /// <summary>
        /// Toplam form sayısı
        /// </summary>
        public int TotalForms { get; set; }

        /// <summary>
        /// Tamamlanma oranı (%)
        /// </summary>
        public double CompletionRate { get; set; }

        /// <summary>
        /// Ortalama tamamlanma süresi (gün)
        /// </summary>
        public double AverageCompletionTime { get; set; }

        /// <summary>
        /// Feedback istatistikleri
        /// </summary>
        public FeedbackStatisticsDto FeedbackStats { get; set; } = new();

        /// <summary>
        /// Rapor oluşturulma tarihi
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Raporu oluşturan kullanıcı
        /// </summary>
        public string GeneratedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// Akademisyen özet bilgileri
    /// </summary>
    public class AcademicianSummaryDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public string AcademicCadre { get; set; } = string.Empty;
        public string Faculty { get; set; } = string.Empty;
    }

    /// <summary>
    /// Rapor dönemi bilgileri
    /// </summary>
    public class ReportPeriodDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string PeriodName { get; set; } = string.Empty; // "2024 Bahar Dönemi"
        public string AcademicYear { get; set; } = string.Empty; // "2023-2024"
    }

    /// <summary>
    /// Kategori bazında performans
    /// </summary>
    public class CategoryPerformanceDto
    {
        public string CategoryId { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public double Score { get; set; }
        public double Weight { get; set; }
        public double WeightedScore { get; set; }
        public int CompletedCriteria { get; set; }
        public int TotalCriteria { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
    }

    /// <summary>
    /// Kriter bazında performans
    /// </summary>
    public class CriterionPerformanceDto
    {
        public string CriterionId { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public string CriterionType { get; set; } = string.Empty; // Static, Dynamic
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public double Weight { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string Status { get; set; } = string.Empty; // Completed, Pending, InProgress
    }

    /// <summary>
    /// Feedback istatistikleri
    /// </summary>
    public class FeedbackStatisticsDto
    {
        public int TotalFeedbacks { get; set; }
        public int ApprovalCount { get; set; }
        public int RejectionCount { get; set; }
        public int RevisionRequestCount { get; set; }
        public double AverageResponseTime { get; set; } // saat cinsinden
        public DateTime? LastFeedbackDate { get; set; }
    }
}
