namespace AcademicPerformance.Models.Dtos
{
    // Evaluation Form DTOs
    public class EvaluationFormDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public List<string>? ApplicableAcademicCadres { get; set; }
        public DateTime EvaluationPeriodStartDate { get; set; }
        public DateTime EvaluationPeriodEndDate { get; set; }
        public DateTime? SubmissionDeadline { get; set; }
        public required string Status { get; set; } // Draft, Active, Archived
        public string? CreatedByUserId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? UpdatedByUserId { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<FormCategoryDto>? Categories { get; set; }
    }

    public class EvaluationFormCreateDto
    {
        public required string Name { get; set; }
        public string? Description { get; set; }
        public List<string>? ApplicableAcademicCadres { get; set; }
        public DateTime EvaluationPeriodStartDate { get; set; }
        public DateTime EvaluationPeriodEndDate { get; set; }
        public DateTime? SubmissionDeadline { get; set; }
    }

    public class EvaluationFormUpdateDto
    {
        public required string Id { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public List<string>? ApplicableAcademicCadres { get; set; }
        public DateTime EvaluationPeriodStartDate { get; set; }
        public DateTime EvaluationPeriodEndDate { get; set; }
        public DateTime? SubmissionDeadline { get; set; }
    }

    public class EvaluationFormStatusUpdateDto
    {
        public required string Id { get; set; }
        public required string Status { get; set; } // Draft, Active, Archived
    }

    // Form Category DTOs
    public class FormCategoryDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public int EvaluationFormAutoIncrementId { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public int DisplayOrder { get; set; }
        public double Weight { get; set; } // e.g., 0.4 for 40%
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public string? CreatedByUserId { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? UpdatedByUserId { get; set; }
        public List<FormCriterionLinkDto>? CriterionLinks { get; set; }
    }

    public class FormCategoryCreateDto
    {
        public required string EvaluationFormId { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public int DisplayOrder { get; set; }
        public double Weight { get; set; }
    }

    public class FormCategoryUpdateDto
    {
        public required string Id { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public int DisplayOrder { get; set; }
        public double Weight { get; set; }
    }

    // Form Criterion Link DTOs
    public class FormCriterionLinkDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public int FormCategoryAutoIncrementId { get; set; }
        public string? StaticCriterionSystemId { get; set; }
        public string? DynamicCriterionTemplateId { get; set; }
        public required string CriterionType { get; set; } // Static, Dynamic
        public string? CriterionName { get; set; } // Pagination filtreleme için
        public int DisplayOrder { get; set; }
        public decimal? WeightPercentage { get; set; }
        public double? Weight { get; set; } // Pagination sıralama için
        public bool IsRequired { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public string? CreatedByUserId { get; set; }
    }

    public class FormCriterionLinkCreateDto
    {
        public required string FormCategoryId { get; set; }
        public string? StaticCriterionSystemId { get; set; }
        public string? DynamicCriterionTemplateId { get; set; }
        public required string CriterionType { get; set; } // Static, Dynamic
        public int DisplayOrder { get; set; }
        public decimal? WeightPercentage { get; set; }
        public bool IsRequired { get; set; }
    }

    public class FormCriterionLinkUpdateDto
    {
        public required string Id { get; set; }
        public int DisplayOrder { get; set; }
        public decimal? WeightPercentage { get; set; }
        public bool IsRequired { get; set; }
    }

    // Bulk operations DTOs
    public class FormCategoryWeightValidationDto
    {
        public required string EvaluationFormId { get; set; }
        public List<CategoryWeightDto> CategoryWeights { get; set; } = new();
    }

    public class CategoryWeightDto
    {
        public required string CategoryId { get; set; }
        public double Weight { get; set; }
    }

    public class CriterionAssignmentDto
    {
        public required string FormCategoryId { get; set; }
        public List<FormCriterionLinkCreateDto> CriterionLinks { get; set; } = new();
    }
}
