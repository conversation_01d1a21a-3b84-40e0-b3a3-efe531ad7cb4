namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Akademisyen statik kriterleri response DTO'su
    /// UI'a gönderilecek format - puan hesaplan<PERSON>mış
    /// </summary>
    public class AcademicianStaticCriteriaResponseDto
    {
        /// <summary>
        /// Akademisyen TC Kimlik Numarası
        /// </summary>
        public required string AcademicianTc { get; set; }
        
        /// <summary>
        /// Veri alınma tarihi
        /// </summary>
        public DateTime DataRetrievalDate { get; set; }
        
        /// <summary>
        /// Toplam kriter sayısı
        /// </summary>
        public int TotalCriteriaCount { get; set; }
        
        /// <summary>
        /// Statik kriter verileri listesi
        /// </summary>
        public required List<StaticCriterionResponseDto> StaticCriteria { get; set; }
    }

    /// <summary>
    /// Statik kriter response DTO'su
    /// Ham değer + katsayı, puan hesaplanmamış
    /// </summary>
    public class StaticCriterionResponseDto
    {
        /// <summary>
        /// Kriter ID'si
        /// </summary>
        public required string CriterionId { get; set; }
        
        /// <summary>
        /// Kriter adı
        /// </summary>
        public required string CriterionName { get; set; }
        
        /// <summary>
        /// Ham değer (ArelBridge'den gelen sayı)
        /// </summary>
        public object? RawValue { get; set; }
        
        /// <summary>
        /// Katsayı (UI'dan değiştirilebilir)
        /// </summary>
        public decimal? Coefficient { get; set; }
        
        /// <summary>
        /// Veri tipi
        /// </summary>
        public required string DataType { get; set; }
        
        /// <summary>
        /// Veri kaynağı
        /// </summary>
        public string? DataSource { get; set; }
        
        /// <summary>
        /// Son güncellenme tarihi
        /// </summary>
        public DateTime LastUpdated { get; set; }
        
        /// <summary>
        /// Cache'den mi geldi?
        /// </summary>
        public bool IsFromCache { get; set; }
    }

    /// <summary>
    /// Toplu statik kriter talep DTO'su
    /// </summary>
    public class BatchStaticCriteriaRequestDto
    {
        /// <summary>
        /// Akademisyen TC listesi
        /// </summary>
        public required List<string> AcademicianTcs { get; set; }
        
        /// <summary>
        /// İstenen kriter ID'leri (opsiyonel, boşsa tümü)
        /// </summary>
        public List<string>? CriterionIds { get; set; }
    }

    /// <summary>
    /// Toplu akademisyen statik kriterleri response DTO'su
    /// </summary>
    public class BatchAcademicianStaticCriteriaResponseDto
    {
        /// <summary>
        /// Talep edilen akademisyen sayısı
        /// </summary>
        public int RequestedAcademicianCount { get; set; }
        
        /// <summary>
        /// İşlenen akademisyen sayısı
        /// </summary>
        public int ProcessedAcademicianCount { get; set; }
        
        /// <summary>
        /// Veri alınma tarihi
        /// </summary>
        public DateTime DataRetrievalDate { get; set; }
        
        /// <summary>
        /// Akademisyen verileri listesi
        /// </summary>
        public required List<AcademicianStaticCriteriaResponseDto> AcademicianData { get; set; }
    }
}
