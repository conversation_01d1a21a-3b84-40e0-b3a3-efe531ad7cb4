namespace AcademicPerformance.Models.Dtos
{
    // Academician Dashboard Main DTO
    public class AcademicianDashboardDto
    {
        public required string AcademicianUserId { get; set; }
        public required string AcademicianName { get; set; }
        public string? AcademicianFullName { get; set; }
        public required string AcademicCadre { get; set; }
        public required string Department { get; set; }
        public string? Email { get; set; }
        public string? Title { get; set; }
        public List<AssignedFormDto> AssignedForms { get; set; } = new();
        public DashboardStatisticsDto Statistics { get; set; } = new();
        public DateTime LastUpdated { get; set; }
        public DateTime? LastProfileSyncedAt { get; set; }
        public bool IsProfileSyncRequired { get; set; }
    }

    // Assigned Form DTO for Dashboard
    public class AssignedFormDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public DateTime EvaluationPeriodStartDate { get; set; }
        public DateTime EvaluationPeriodEndDate { get; set; }
        public DateTime? SubmissionDeadline { get; set; }
        public required string FormStatus { get; set; } // Draft, Active, Archived
        public string Status { get; set; } = string.Empty; // Pagination filtreleme için
        public DateTime CreatedAt { get; set; }
        public SubmissionStatusDto? SubmissionStatus { get; set; }
        public bool IsDeadlineApproaching { get; set; }
        public int? DaysUntilDeadline { get; set; }
        public bool CanEdit { get; set; }
        public bool CanSubmit { get; set; }
    }

    // Submission Status DTO
    public class SubmissionStatusDto
    {
        public string? SubmissionId { get; set; }
        public required string Status { get; set; } // NotStarted, Draft, Submitted, UnderReview, Approved, Rejected, RequiresRevision
        public DateTime? SubmittedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public string? ApprovedByControllerUserId { get; set; }
        public string? ApprovedByControllerName { get; set; }
        public DateTime? RejectedAt { get; set; }
        public string? RejectedByControllerUserId { get; set; }
        public string? RejectedByControllerName { get; set; }
        public string? ApprovalComments { get; set; }
        public string? RejectionComments { get; set; }
        public double? CompletionPercentage { get; set; }
    }

    // Dashboard Statistics DTO
    public class DashboardStatisticsDto
    {
        public int TotalAssignedForms { get; set; }
        public int NotStartedForms { get; set; }
        public int DraftForms { get; set; }
        public int SubmittedForms { get; set; }
        public int ApprovedForms { get; set; }
        public int RejectedForms { get; set; }
        public int FormsRequiringRevision { get; set; }
        public int FormsWithApproachingDeadlines { get; set; }
        public double OverallCompletionPercentage { get; set; }
    }

    // Form Detail Request DTO
    public class FormDetailRequestDto
    {
        public required string FormId { get; set; }
        public string? SubmissionId { get; set; }
    }

    // Quick Action DTOs
    public class QuickActionDto
    {
        public required string ActionType { get; set; } // StartSubmission, ContinueSubmission, ViewSubmission
        public required string FormId { get; set; }
        public string? SubmissionId { get; set; }
        public required string ActionText { get; set; }
        public bool IsEnabled { get; set; }
    }

    // Academician Profile DTOs for sync operations
    public class AcademicianProfileDto
    {
        public string? Id { get; set; }
        public int? AutoIncrementId { get; set; }
        public required string UniversityUserId { get; set; }
        public required string Name { get; set; }
        public required string Surname { get; set; }
        public string? FullName { get; set; }
        public string? Department { get; set; }
        public string? AcademicCadre { get; set; }
        public string? Email { get; set; }
        public string? Title { get; set; }
        public DateTime LastSyncedAt { get; set; }
        public bool IsActive { get; set; }
        public string? SyncNotes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class AcademicianProfileSyncDto
    {
        public required string UniversityUserId { get; set; }
        public required string Name { get; set; }
        public required string Surname { get; set; }
        public string? Department { get; set; }
        public string? AcademicCadre { get; set; }
        public string? Email { get; set; }
        public string? Title { get; set; }
        public bool ForceSync { get; set; } = false;
    }

    public class AcademicianProfileSyncResultDto
    {
        public required string UniversityUserId { get; set; }
        public bool Success { get; set; }
        public required string Action { get; set; } // Created, Updated, NoChange, Error
        public string? ErrorMessage { get; set; }
        public DateTime SyncedAt { get; set; }
        public AcademicianProfileDto? Profile { get; set; }
    }

    public class BulkProfileSyncRequestDto
    {
        public List<string> UniversityUserIds { get; set; } = new();
        public bool ForceSync { get; set; } = false;
        public bool SyncAll { get; set; } = false;
    }

    public class BulkProfileSyncResultDto
    {
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int ErrorCount { get; set; }
        public int NoChangeCount { get; set; }
        public List<AcademicianProfileSyncResultDto> Results { get; set; } = new();
        public DateTime SyncStartedAt { get; set; }
        public DateTime SyncCompletedAt { get; set; }
    }
}
