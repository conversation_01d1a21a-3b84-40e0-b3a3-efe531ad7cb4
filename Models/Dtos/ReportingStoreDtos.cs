using AcademicPerformance.Models.Cos;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Akademisyen performans verisi (store için)
    /// </summary>
    public class AcademicianPerformanceDataDto
    {
        public string AcademicianUserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public string AcademicCadre { get; set; } = string.Empty;
        public double OverallScore { get; set; }
        public int CompletedForms { get; set; }
        public int TotalForms { get; set; }
        public double CompletionRate { get; set; }
        public double AverageCompletionTime { get; set; }
        public List<CategoryPerformanceDto> CategoryScores { get; set; } = new();
        public List<CriterionPerformanceDto> CriterionScores { get; set; } = new();
    }

    /// <summary>
    /// Bölüm performans verisi (store için)
    /// </summary>
    public class DepartmentPerformanceDataDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public double OverallScore { get; set; }
        public int TotalAcademicians { get; set; }
        public int ActiveAcademicians { get; set; }
        public List<AcademicianPerformanceSummaryDto> AcademicianPerformances { get; set; } = new();
        public List<CategoryPerformanceDto> CategoryPerformances { get; set; } = new();
    }

    /// <summary>
    /// Kriter analiz verisi (store için)
    /// </summary>
    public class CriterionAnalysisDataDto
    {
        public string CriterionId { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public string CriterionType { get; set; } = string.Empty;
        public CriterionStatisticsDto Statistics { get; set; } = new();
        public List<DepartmentCriterionAnalysisDto> DepartmentAnalyses { get; set; } = new();
        public List<AcademicianCriterionPerformanceDto> AcademicianPerformances { get; set; } = new();
    }

    /// <summary>
    /// Karşılaştırmalı veri (store için)
    /// </summary>
    public class ComparativeDataDto
    {
        public double AcademicianScore { get; set; }
        public double DepartmentAverage { get; set; }
        public double FacultyAverage { get; set; }
        public double UniversityAverage { get; set; }
        public int DepartmentRank { get; set; }
        public int FacultyRank { get; set; }
        public int UniversityRank { get; set; }
        public List<CategoryComparisonDto> CategoryComparisons { get; set; } = new();
    }

    /// <summary>
    /// Gelişmiş bölüm raporu DTO
    /// </summary>
    public class AdvancedDepartmentReportDto
    {
        public string ReportId { get; set; } = string.Empty;
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public DateRangeDto ReportPeriod { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
        public string GeneratedBy { get; set; } = string.Empty;

        // Temel veriler
        public int TotalAcademicians { get; set; }
        public int ActiveAcademicians { get; set; }
        public double AverageScore { get; set; }

        // Gelişmiş analiz verileri
        public PerformanceDistributionDto PerformanceDistribution { get; set; } = new();
        public DepartmentFeedbackStatsDto FeedbackStatistics { get; set; } = new();
        public Dictionary<string, double> CriterionPerformance { get; set; } = new();

        // Filtreleme kriterleri
        public AdvancedReportFilterCo AppliedFilters { get; set; } = new();

        // Detaylı akademisyen verileri
        public List<AcademicianPerformanceSummaryDto> AcademicianPerformances { get; set; } = new();
        public List<AcademicianPerformanceSummaryDto> TopPerformers { get; set; } = new();
        public List<AcademicianPerformanceSummaryDto> NeedsImprovement { get; set; } = new();
    }

    /// <summary>
    /// Çoklu akademisyen karşılaştırma DTO
    /// </summary>
    public class MultiAcademicianComparisonDto
    {
        public string ComparisonId { get; set; } = string.Empty;
        public DateRangeDto ComparisonPeriod { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
        public string GeneratedBy { get; set; } = string.Empty;

        public List<AcademicianComparisonItemDto> AcademicianComparisons { get; set; } = new();
        public ComparisonStatisticsDto ComparisonStatistics { get; set; } = new();
        public MultiAcademicianComparisonCo ComparisonCriteria { get; set; } = new();
    }

    /// <summary>
    /// Akademisyen karşılaştırma öğesi DTO
    /// </summary>
    public class AcademicianComparisonItemDto
    {
        public string AcademicianUserId { get; set; } = string.Empty;
        public string AcademicianName { get; set; } = string.Empty;
        public double OverallScore { get; set; }
        public Dictionary<string, double> CategoryScores { get; set; } = new();
        public double CompletionRate { get; set; }
        public double AverageCompletionTime { get; set; }
        public int TotalForms { get; set; }
        public int CompletedForms { get; set; }
    }

    /// <summary>
    /// Karşılaştırma istatistikleri DTO
    /// </summary>
    public class ComparisonStatisticsDto
    {
        public double HighestScore { get; set; }
        public double LowestScore { get; set; }
        public double AverageScore { get; set; }
        public double ScoreStandardDeviation { get; set; }
    }

    /// <summary>
    /// Performans trend analizi DTO
    /// </summary>
    public class PerformanceTrendAnalysisDto
    {
        public string AnalysisId { get; set; } = string.Empty;
        public string AcademicianUserId { get; set; } = string.Empty;
        public DateRangeDto AnalysisPeriod { get; set; } = new();
        public string IntervalType { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        public string GeneratedBy { get; set; } = string.Empty;

        public List<PerformanceTrendDataPointDto> TrendData { get; set; } = new();
        public TrendAnalysisResultDto TrendAnalysis { get; set; } = new();
        public TrendAnalysisCo AnalysisCriteria { get; set; } = new();
    }

    /// <summary>
    /// Performans trend veri noktası DTO
    /// </summary>
    public class PerformanceTrendDataPointDto
    {
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public double OverallScore { get; set; }
        public double CompletionRate { get; set; }
        public Dictionary<string, double> CategoryScores { get; set; } = new();
        public int FormCount { get; set; }
    }

    /// <summary>
    /// Trend analizi sonucu DTO
    /// </summary>
    public class TrendAnalysisResultDto
    {
        public string TrendDirection { get; set; } = string.Empty; // Increasing, Decreasing, Stable
        public double TrendStrength { get; set; }
        public double AverageGrowthRate { get; set; }
        public double Volatility { get; set; }
    }



    /// <summary>
    /// Tarih aralığı DTO
    /// </summary>
    public class DateRangeDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    /// <summary>
    /// İstatistiksel analiz sonucu DTO
    /// </summary>
    public class StatisticalAnalysisResultDto
    {
        public string AnalysisId { get; set; } = string.Empty;
        public string AnalysisType { get; set; } = string.Empty;
        public DateTime AnalysisDate { get; set; }
        public string Status { get; set; } = string.Empty;

        // Temel istatistikler
        public Dictionary<string, double> BasicStatistics { get; set; } = new();

        // Korelasyon matrisi
        public Dictionary<string, Dictionary<string, double>>? CorrelationMatrix { get; set; }

        // Regresyon analizi sonuçları
        public RegressionAnalysisDto? RegressionResults { get; set; }

        // ANOVA sonuçları
        public AnovaAnalysisDto? AnovaResults { get; set; }

        // Güven aralıkları
        public Dictionary<string, ConfidenceIntervalDto>? ConfidenceIntervals { get; set; }

        // P-değerleri
        public Dictionary<string, double>? PValues { get; set; }

        // Sonuç özeti
        public string Summary { get; set; } = string.Empty;
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Dashboard veri DTO
    /// </summary>
    public class DashboardDataDto
    {
        public string DashboardId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string DashboardType { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        public string TimeRange { get; set; } = string.Empty;

        // Widget verileri
        public Dictionary<string, object> WidgetData { get; set; } = new();

        // KPI'lar
        public List<KpiDataDto> KpiData { get; set; } = new();

        // Grafikler
        public List<ChartDataDto> ChartData { get; set; } = new();

        // Tablolar
        public List<TableDataDto> TableData { get; set; } = new();

        // Gerçek zamanlı veriler
        public Dictionary<string, object>? RealtimeData { get; set; }

        // Filtre durumu
        public Dictionary<string, object> AppliedFilters { get; set; } = new();
    }

    /// <summary>
    /// Performans metrikleri sonucu DTO
    /// </summary>
    public class PerformanceMetricsResultDto
    {
        public string MetricsId { get; set; } = string.Empty;
        public string MetricType { get; set; } = string.Empty;
        public DateTime CalculatedAt { get; set; }
        public DateRangeDto CalculationPeriod { get; set; } = new();

        // Hesaplanan metrikler
        public Dictionary<string, double> CalculatedMetrics { get; set; } = new();

        // Karşılaştırma verileri
        public Dictionary<string, ComparisonDataDto>? ComparisonData { get; set; }

        // Trend verileri
        public List<MetricTrendPointDto>? TrendData { get; set; }

        // Benchmark verileri
        public Dictionary<string, double>? BenchmarkValues { get; set; }

        // Performans skorları
        public Dictionary<string, PerformanceScoreDto> PerformanceScores { get; set; } = new();
    }

    /// <summary>
    /// Gerçek zamanlı analiz sonucu DTO
    /// </summary>
    public class RealtimeAnalysisResultDto
    {
        public string AnalysisId { get; set; } = string.Empty;
        public string AnalysisType { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public int AnalysisWindowMinutes { get; set; }

        // Anlık veriler
        public Dictionary<string, object> CurrentData { get; set; } = new();

        // Trend göstergeleri
        public Dictionary<string, TrendIndicatorDto> TrendIndicators { get; set; } = new();

        // Alarmlar
        public List<AlertDto> ActiveAlerts { get; set; } = new();

        // Performans göstergeleri
        public Dictionary<string, double> PerformanceIndicators { get; set; } = new();

        // Sonraki güncelleme zamanı
        public DateTime NextUpdateTime { get; set; }
    }

    /// <summary>
    /// Batch analiz sonucu DTO
    /// </summary>
    public class BatchAnalysisResultDto
    {
        public string JobId { get; set; } = string.Empty;
        public string AnalysisType { get; set; } = string.Empty;
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string Status { get; set; } = string.Empty; // Pending, Running, Completed, Failed
        public int ProgressPercentage { get; set; }

        // İşlem detayları
        public BatchJobDetailsDto JobDetails { get; set; } = new();

        // Sonuçlar (tamamlandığında)
        public Dictionary<string, object>? Results { get; set; }

        // Hata bilgileri (varsa)
        public List<string>? ErrorMessages { get; set; }

        // İstatistikler
        public BatchJobStatisticsDto? Statistics { get; set; }
    }

    #region Supporting DTOs

    /// <summary>
    /// Regresyon analizi DTO
    /// </summary>
    public class RegressionAnalysisDto
    {
        public double RSquared { get; set; }
        public double AdjustedRSquared { get; set; }
        public Dictionary<string, double> Coefficients { get; set; } = new();
        public Dictionary<string, double> StandardErrors { get; set; } = new();
        public double FStatistic { get; set; }
        public double PValue { get; set; }
    }

    /// <summary>
    /// ANOVA analizi DTO
    /// </summary>
    public class AnovaAnalysisDto
    {
        public double FStatistic { get; set; }
        public double PValue { get; set; }
        public int DegreesOfFreedom { get; set; }
        public double SumOfSquares { get; set; }
        public double MeanSquare { get; set; }
        public List<GroupComparisonDto> GroupComparisons { get; set; } = new();
    }

    /// <summary>
    /// Güven aralığı DTO
    /// </summary>
    public class ConfidenceIntervalDto
    {
        public double LowerBound { get; set; }
        public double UpperBound { get; set; }
        public double ConfidenceLevel { get; set; }
        public double Mean { get; set; }
    }

    /// <summary>
    /// KPI veri DTO
    /// </summary>
    public class KpiDataDto
    {
        public string KpiId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public double CurrentValue { get; set; }
        public double? TargetValue { get; set; }
        public double? PreviousValue { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string TrendDirection { get; set; } = string.Empty; // Up, Down, Stable
        public double ChangePercentage { get; set; }
    }

    /// <summary>
    /// Grafik veri DTO
    /// </summary>
    public class ChartDataDto
    {
        public string ChartId { get; set; } = string.Empty;
        public string ChartType { get; set; } = string.Empty; // Line, Bar, Pie, etc.
        public string Title { get; set; } = string.Empty;
        public List<ChartSeriesDto> Series { get; set; } = new();
        public List<string> Categories { get; set; } = new();
        public ChartOptionsDto Options { get; set; } = new();
    }

    /// <summary>
    /// Tablo veri DTO
    /// </summary>
    public class TableDataDto
    {
        public string TableId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public List<TableColumnDto> Columns { get; set; } = new();
        public List<Dictionary<string, object>> Rows { get; set; } = new();
        public int TotalRows { get; set; }
        public TablePaginationDto? Pagination { get; set; }
    }

    /// <summary>
    /// Karşılaştırma veri DTO
    /// </summary>
    public class ComparisonDataDto
    {
        public double CurrentValue { get; set; }
        public double ComparisonValue { get; set; }
        public double ChangeAmount { get; set; }
        public double ChangePercentage { get; set; }
        public string ChangeDirection { get; set; } = string.Empty; // Up, Down, Stable
        public string ComparisonType { get; set; } = string.Empty; // Previous, Benchmark, Average
    }

    /// <summary>
    /// Metrik trend noktası DTO
    /// </summary>
    public class MetricTrendPointDto
    {
        public DateTime Date { get; set; }
        public double Value { get; set; }
        public string MetricName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Performans skoru DTO
    /// </summary>
    public class PerformanceScoreDto
    {
        public double Score { get; set; }
        public string Grade { get; set; } = string.Empty; // A, B, C, D, F
        public string Category { get; set; } = string.Empty;
        public double MaxPossibleScore { get; set; }
        public double WeightedScore { get; set; }
    }

    /// <summary>
    /// Trend göstergesi DTO
    /// </summary>
    public class TrendIndicatorDto
    {
        public string IndicatorName { get; set; } = string.Empty;
        public double CurrentValue { get; set; }
        public string TrendDirection { get; set; } = string.Empty; // Up, Down, Stable
        public double ChangeRate { get; set; }
        public string Significance { get; set; } = string.Empty; // High, Medium, Low
    }

    /// <summary>
    /// Alarm DTO
    /// </summary>
    public class AlertDto
    {
        public string AlertId { get; set; } = string.Empty;
        public string AlertType { get; set; } = string.Empty; // Warning, Critical, Info
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string Source { get; set; } = string.Empty;
        public Dictionary<string, object> AlertData { get; set; } = new();
        public bool IsAcknowledged { get; set; }
    }

    /// <summary>
    /// Batch iş detayları DTO
    /// </summary>
    public class BatchJobDetailsDto
    {
        public string Priority { get; set; } = string.Empty;
        public int EstimatedDurationMinutes { get; set; }
        public int DatasetSize { get; set; }
        public DateTime? EstimatedCompletionTime { get; set; }
        public List<string> ProcessingSteps { get; set; } = new();
        public Dictionary<string, object> JobParameters { get; set; } = new();
    }

    /// <summary>
    /// Batch iş istatistikleri DTO
    /// </summary>
    public class BatchJobStatisticsDto
    {
        public int TotalRecordsProcessed { get; set; }
        public int SuccessfulRecords { get; set; }
        public int FailedRecords { get; set; }
        public double ProcessingRate { get; set; } // records per minute
        public TimeSpan ActualDuration { get; set; }
        public Dictionary<string, int> StepStatistics { get; set; } = new();
    }

    /// <summary>
    /// Grup karşılaştırması DTO
    /// </summary>
    public class GroupComparisonDto
    {
        public string GroupName { get; set; } = string.Empty;
        public double Mean { get; set; }
        public double StandardDeviation { get; set; }
        public int SampleSize { get; set; }
        public double PValue { get; set; }
        public bool IsSignificant { get; set; }
    }

    /// <summary>
    /// Grafik serisi DTO
    /// </summary>
    public class ChartSeriesDto
    {
        public string Name { get; set; } = string.Empty;
        public List<double> Data { get; set; } = new();
        public string Color { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // line, bar, area, etc.
    }

    /// <summary>
    /// Grafik seçenekleri DTO
    /// </summary>
    public class ChartOptionsDto
    {
        public string XAxisTitle { get; set; } = string.Empty;
        public string YAxisTitle { get; set; } = string.Empty;
        public bool ShowLegend { get; set; } = true;
        public bool ShowGrid { get; set; } = true;
        public Dictionary<string, object> CustomOptions { get; set; } = new();
    }

    /// <summary>
    /// Tablo sütunu DTO
    /// </summary>
    public class TableColumnDto
    {
        public string ColumnId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty; // string, number, date, boolean
        public bool IsSortable { get; set; } = true;
        public bool IsFilterable { get; set; } = true;
        public string Format { get; set; } = string.Empty;
    }

    /// <summary>
    /// Tablo sayfalama DTO
    /// </summary>
    public class TablePaginationDto
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public int TotalRecords { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    /// <summary>
    /// Export dosya sonucu DTO
    /// </summary>
    public class ExportFileResultDto
    {
        public string FileId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime CreatedAt { get; set; }
        public string DownloadUrl { get; set; } = string.Empty;
        public DateTime? ExpiresAt { get; set; }
        public string Status { get; set; } = string.Empty; // Processing, Completed, Failed
        public Dictionary<string, object>? Metadata { get; set; }
    }

    /// <summary>
    /// Export durumu DTO
    /// </summary>
    public class ExportStatusDto
    {
        public string ExportId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Pending, Processing, Completed, Failed
        public int ProgressPercentage { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? ErrorMessage { get; set; }
        public ExportFileResultDto? FileResult { get; set; }
        public Dictionary<string, object>? ProcessingDetails { get; set; }
    }

    /// <summary>
    /// Dosya indirme sonucu DTO
    /// </summary>
    public class FileDownloadResultDto
    {
        public Stream FileStream { get; set; } = Stream.Null;
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime LastModified { get; set; }
        public Dictionary<string, string>? Headers { get; set; }
    }

    /// <summary>
    /// Bulk export sonucu DTO
    /// </summary>
    public class BulkExportResultDto
    {
        public string JobId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Pending, Processing, Completed, Failed
        public int TotalRequests { get; set; }
        public int CompletedRequests { get; set; }
        public int FailedRequests { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public List<BulkExportItemResultDto> ItemResults { get; set; } = new();
        public ExportFileResultDto? ZipFileResult { get; set; }
        public List<string>? ErrorMessages { get; set; }
    }

    /// <summary>
    /// Bulk export öğe sonucu DTO
    /// </summary>
    public class BulkExportItemResultDto
    {
        public string ItemId { get; set; } = string.Empty;
        public string ExportType { get; set; } = string.Empty;
        public string ReportType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Completed, Failed
        public ExportFileResultDto? FileResult { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime ProcessedAt { get; set; }
    }

    /// <summary>
    /// Margin ayarları DTO
    /// </summary>
    public class MarginSettingsDto
    {
        public double Top { get; set; } = 20;
        public double Right { get; set; } = 20;
        public double Bottom { get; set; } = 20;
        public double Left { get; set; } = 20;
    }

    /// <summary>
    /// Font ayarları DTO
    /// </summary>
    public class FontSettingsDto
    {
        public string FontFamily { get; set; } = "Arial";
        public int FontSize { get; set; } = 12;
        public string FontColor { get; set; } = "#000000";
        public bool Bold { get; set; } = false;
        public bool Italic { get; set; } = false;
    }

    /// <summary>
    /// Hücre stil DTO
    /// </summary>
    public class CellStyleDto
    {
        public string BackgroundColor { get; set; } = "#FFFFFF";
        public string FontColor { get; set; } = "#000000";
        public string FontFamily { get; set; } = "Arial";
        public int FontSize { get; set; } = 12;
        public bool Bold { get; set; } = false;
        public bool Italic { get; set; } = false;
        public string BorderStyle { get; set; } = "None";
        public string BorderColor { get; set; } = "#000000";
        public string TextAlign { get; set; } = "Left";
    }

    #endregion
}
