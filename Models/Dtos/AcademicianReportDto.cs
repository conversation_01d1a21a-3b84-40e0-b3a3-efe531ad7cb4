using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Detaylı akademisyen raporu DTO'su
    /// </summary>
    public class AcademicianReportDto
    {
        /// <summary>
        /// Rapor ID'si
        /// </summary>
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// Akademisyen detay bilgileri
        /// </summary>
        public AcademicianDetailDto Academician { get; set; } = new();

        /// <summary>
        /// Rapor dönemi
        /// </summary>
        public ReportPeriodDto Period { get; set; } = new();

        /// <summary>
        /// Performans özeti
        /// </summary>
        public PerformanceSummaryDto PerformanceSummary { get; set; } = new();

        /// <summary>
        /// Form bazında detay performans
        /// </summary>
        public List<FormPerformanceDetailDto> FormPerformances { get; set; } = new();

        /// <summary>
        /// Kategori bazında detay analiz
        /// </summary>
        public List<CategoryDetailAnalysisDto> CategoryAnalyses { get; set; } = new();

        /// <summary>
        /// Kriter bazında detay performans
        /// </summary>
        public List<CriterionDetailPerformanceDto> CriterionPerformances { get; set; } = new();

        /// <summary>
        /// Feedback detayları
        /// </summary>
        public List<FeedbackDetailDto> FeedbackDetails { get; set; } = new();

        /// <summary>
        /// Zaman bazında performans trendi
        /// </summary>
        public List<TimeBasedPerformanceDto> PerformanceTrend { get; set; } = new();

        /// <summary>
        /// Karşılaştırmalı analiz (bölüm ortalaması ile)
        /// </summary>
        public ComparativeAnalysisDto ComparativeAnalysis { get; set; } = new();

        /// <summary>
        /// Gelişim önerileri
        /// </summary>
        public List<ImprovementSuggestionDto> ImprovementSuggestions { get; set; } = new();

        /// <summary>
        /// Rapor oluşturulma tarihi
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Raporu oluşturan kullanıcı
        /// </summary>
        public string GeneratedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// Akademisyen detay bilgileri
    /// </summary>
    public class AcademicianDetailDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public string Faculty { get; set; } = string.Empty;
        public string AcademicCadre { get; set; } = string.Empty;
        public DateTime HireDate { get; set; }
        public int ExperienceYears { get; set; }
        public string EmployeeId { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Office { get; set; } = string.Empty;
    }

    /// <summary>
    /// Performans özeti
    /// </summary>
    public class PerformanceSummaryDto
    {
        public double OverallScore { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
        public int DepartmentRank { get; set; }
        public int FacultyRank { get; set; }
        public int UniversityRank { get; set; }
        public double DepartmentAverageScore { get; set; }
        public double FacultyAverageScore { get; set; }
        public double UniversityAverageScore { get; set; }
        public string PerformanceTrend { get; set; } = string.Empty; // Improving, Declining, Stable
    }

    /// <summary>
    /// Form performans detayı
    /// </summary>
    public class FormPerformanceDetailDto
    {
        public string FormId { get; set; } = string.Empty;
        public string FormName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public double CompletionPercentage { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public int DaysToComplete { get; set; }
        public int FeedbackCount { get; set; }
        public int RevisionCount { get; set; }
        public string LastFeedbackType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Kategori detay analizi
    /// </summary>
    public class CategoryDetailAnalysisDto
    {
        public string CategoryId { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public double Weight { get; set; }
        public double WeightedScore { get; set; }
        public double DepartmentAverage { get; set; }
        public double FacultyAverage { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
        public int CompletedCriteria { get; set; }
        public int TotalCriteria { get; set; }
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
    }

    /// <summary>
    /// Kriter detay performansı
    /// </summary>
    public class CriterionDetailPerformanceDto
    {
        public string CriterionId { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public string CriterionType { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public double Weight { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Evidence { get; set; } = string.Empty;
        public List<string> AttachedFiles { get; set; } = new();
        public string Comments { get; set; } = string.Empty;
        public double DepartmentAverage { get; set; }
    }

    /// <summary>
    /// Feedback detayı
    /// </summary>
    public class FeedbackDetailDto
    {
        public string FeedbackId { get; set; } = string.Empty;
        public string FeedbackType { get; set; } = string.Empty;
        public string FormName { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string ControllerName { get; set; } = string.Empty;
        public bool IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string Response { get; set; } = string.Empty;
    }

    /// <summary>
    /// Zaman bazında performans
    /// </summary>
    public class TimeBasedPerformanceDto
    {
        public DateTime Date { get; set; }
        public double Score { get; set; }
        public int CompletedForms { get; set; }
        public string Period { get; set; } = string.Empty; // Weekly, Monthly, Quarterly
    }

    /// <summary>
    /// Karşılaştırmalı analiz
    /// </summary>
    public class ComparativeAnalysisDto
    {
        public double ScoreVsDepartmentAverage { get; set; }
        public double ScoreVsFacultyAverage { get; set; }
        public double CompletionRateVsDepartmentAverage { get; set; }
        public double CompletionRateVsFacultyAverage { get; set; }
        public List<CategoryComparisonDto> CategoryComparisons { get; set; } = new();
    }

    /// <summary>
    /// Kategori karşılaştırması
    /// </summary>
    public class CategoryComparisonDto
    {
        public string CategoryName { get; set; } = string.Empty;
        public double MyScore { get; set; }
        public double DepartmentAverage { get; set; }
        public double FacultyAverage { get; set; }
        public string ComparisonResult { get; set; } = string.Empty; // Above, Below, Equal
    }

    /// <summary>
    /// Gelişim önerisi
    /// </summary>
    public class ImprovementSuggestionDto
    {
        public string Area { get; set; } = string.Empty;
        public string Suggestion { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public List<string> ActionItems { get; set; } = new();
        public string ExpectedImpact { get; set; } = string.Empty;
    }
}
