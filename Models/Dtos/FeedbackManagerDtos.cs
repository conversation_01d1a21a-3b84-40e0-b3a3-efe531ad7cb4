namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Feedback filtreleme için Co sınıfı
    /// </summary>
    public class FeedbackFilterCo
    {
        public string? FeedbackType { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? SubmissionStatus { get; set; }
        public string? FormId { get; set; }
        public string? Priority { get; set; }
        public bool? HasResponse { get; set; }
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// Ta<PERSON>h aralığı için helper sınıf
    /// </summary>
    public class DateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    /// <summary>
    /// Kriter revision istatistikleri için DTO
    /// </summary>
    public class CriterionRevisionStatDto
    {
        public string CriterionId { get; set; } = string.Empty;
        public string CriterionName { get; set; } = string.Empty;
        public int RevisionCount { get; set; }
        public double RevisionPercentage { get; set; }
    }
}
