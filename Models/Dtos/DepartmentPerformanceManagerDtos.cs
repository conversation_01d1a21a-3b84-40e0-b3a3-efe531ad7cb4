namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Dosya export sonucu DTO'su
    /// </summary>
    public class FileExportResultDto
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime CreatedAt { get; set; }
        public string DownloadUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// Doğrulama sonucu DTO'su
    /// </summary>
    public class ValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> ValidationData { get; set; } = new();
    }
}
