using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Ders verification DTO'su - Portfolio kontrol modülü için
    /// </summary>
    public class CourseVerificationDto
    {
        /// <summary>
        /// Verification ID'si
        /// </summary>
        public string Id { get; set; } = null!;

        /// <summary>
        /// Akademisyen TC Kimlik Numarası
        /// </summary>
        public string AcademicianTc { get; set; } = null!;

        /// <summary>
        /// Ders kodu (örn: BIL101, MAT201)
        /// </summary>
        public string CourseCode { get; set; } = null!;

        /// <summary>
        /// Dönem adı (örn: 2024-2025-Güz, 2024-2025-Bahar)
        /// </summary>
        public string PeriodName { get; set; } = null!;

        /// <summary>
        /// Ders adı (örn: Bilgisayar Programlama, Matematik I)
        /// </summary>
        public string CourseName { get; set; } = null!;

        /// <summary>
        /// Sınav kâğıtları verification durumu
        /// </summary>
        public string ExamPapersStatus { get; set; } = "Pending";

        /// <summary>
        /// Yanıt anahtarı verification durumu
        /// </summary>
        public string AnswerKeyStatus { get; set; } = "Pending";

        /// <summary>
        /// Sınav tutanağı verification durumu
        /// </summary>
        public string ExamRecordStatus { get; set; } = "Pending";

        /// <summary>
        /// Yoklama kâğıdı verification durumu
        /// </summary>
        public string AttendanceSheetStatus { get; set; } = "Pending";

        /// <summary>
        /// Ders izlencesi verification durumu
        /// </summary>
        public string CourseSyllabusStatus { get; set; } = "Pending";

        /// <summary>
        /// 14 haftalık ders yoklama listeleri verification durumu
        /// </summary>
        public string WeeklyAttendanceStatus { get; set; } = "Pending";

        /// <summary>
        /// Bütünleme sınavından alınmış not çizelgesi verification durumu
        /// </summary>
        public string MakeupExamGradesStatus { get; set; } = "Pending";

        /// <summary>
        /// UZEM Sistemi'nde ders ve sınav yoklamaları verification durumu
        /// </summary>
        public string UzemRecordsStatus { get; set; } = "Pending";

        /// <summary>
        /// Genel verification notları
        /// </summary>
        public string? VerificationNotes { get; set; }

        /// <summary>
        /// Son verification tarihi
        /// </summary>
        public DateTime? LastVerifiedAt { get; set; }

        /// <summary>
        /// Son verification yapan archivist kullanıcı ID'si
        /// </summary>
        public string? LastVerifiedByArchivistId { get; set; }

        /// <summary>
        /// EBYS referans ID'si
        /// </summary>
        public string? EbysReferenceId { get; set; }

        /// <summary>
        /// Oluşturulma tarihi
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Güncellenme tarihi
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Genel verification durumu
        /// </summary>
        public string OverallStatus { get; set; } = "Pending";

        /// <summary>
        /// Tamamlanma yüzdesi
        /// </summary>
        public double CompletionPercentage { get; set; }

        /// <summary>
        /// Bekleyen verification sayısı
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// Sorunlu verification sayısı
        /// </summary>
        public int IssueCount { get; set; }
    }

    /// <summary>
    /// Ders verification güncelleme DTO'su
    /// </summary>
    public class CourseVerificationUpdateDto
    {
        /// <summary>
        /// Verification ID'si
        /// </summary>
        [Required]
        public string Id { get; set; } = null!;

        /// <summary>
        /// Sınav kâğıtları verification durumu
        /// </summary>
        public string? ExamPapersStatus { get; set; }

        /// <summary>
        /// Yanıt anahtarı verification durumu
        /// </summary>
        public string? AnswerKeyStatus { get; set; }

        /// <summary>
        /// Sınav tutanağı verification durumu
        /// </summary>
        public string? ExamRecordStatus { get; set; }

        /// <summary>
        /// Yoklama kâğıdı verification durumu
        /// </summary>
        public string? AttendanceSheetStatus { get; set; }

        /// <summary>
        /// Ders izlencesi verification durumu
        /// </summary>
        public string? CourseSyllabusStatus { get; set; }

        /// <summary>
        /// 14 haftalık ders yoklama listeleri verification durumu
        /// </summary>
        public string? WeeklyAttendanceStatus { get; set; }

        /// <summary>
        /// Bütünleme sınavından alınmış not çizelgesi verification durumu
        /// </summary>
        public string? MakeupExamGradesStatus { get; set; }

        /// <summary>
        /// UZEM Sistemi'nde ders ve sınav yoklamaları verification durumu
        /// </summary>
        public string? UzemRecordsStatus { get; set; }

        /// <summary>
        /// Genel verification notları
        /// </summary>
        public string? VerificationNotes { get; set; }

        /// <summary>
        /// EBYS referans ID'si
        /// </summary>
        public string? EbysReferenceId { get; set; }
    }

    /// <summary>
    /// Toplu ders verification güncelleme DTO'su
    /// </summary>
    public class BulkCourseVerificationUpdateDto
    {
        /// <summary>
        /// Güncellenecek verification ID'leri
        /// </summary>
        [Required]
        public List<string> VerificationIds { get; set; } = new();

        /// <summary>
        /// Güncellenecek verification listesi
        /// </summary>
        [Required]
        public List<CourseVerificationUpdateDto> Updates { get; set; } = new();

        /// <summary>
        /// Toplu güncelleme notları
        /// </summary>
        public string? BulkUpdateNotes { get; set; }
    }

    /// <summary>
    /// Ders verification oluşturma DTO'su
    /// </summary>
    public class CreateCourseVerificationDto
    {
        /// <summary>
        /// Akademisyen TC Kimlik Numarası
        /// </summary>
        [Required]
        [StringLength(11, MinimumLength = 11)]
        public string AcademicianTc { get; set; } = null!;

        /// <summary>
        /// Ders kodu (örn: BIL101, MAT201)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CourseCode { get; set; } = null!;

        /// <summary>
        /// Dönem adı (örn: 2024-2025-Güz, 2024-2025-Bahar)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PeriodName { get; set; } = null!;

        /// <summary>
        /// Ders adı (örn: Bilgisayar Programlama, Matematik I)
        /// </summary>
        [Required]
        [StringLength(200)]
        public string CourseName { get; set; } = null!;
    }

    /// <summary>
    /// Archivist dashboard response DTO'su
    /// </summary>
    public class ArchivistDashboardResponseDto
    {
        /// <summary>
        /// Bekleyen verification'lar
        /// </summary>
        public List<CourseVerificationDto> PendingVerifications { get; set; } = new();

        /// <summary>
        /// Son güncellenen verification'lar
        /// </summary>
        public List<CourseVerificationDto> RecentlyUpdated { get; set; } = new();

        /// <summary>
        /// Dashboard istatistikleri
        /// </summary>
        public VerificationDashboardStatisticsDto Statistics { get; set; } = new();

        /// <summary>
        /// Archivist'in kendi istatistikleri
        /// </summary>
        public CourseVerificationStatisticsDto ArchivistStatistics { get; set; } = new();
    }

    /// <summary>
    /// Verification dashboard istatistikleri DTO'su
    /// </summary>
    public class VerificationDashboardStatisticsDto
    {
        /// <summary>
        /// Toplam verification sayısı
        /// </summary>
        public int TotalVerifications { get; set; }

        /// <summary>
        /// Bekleyen verification sayısı
        /// </summary>
        public int PendingVerifications { get; set; }

        /// <summary>
        /// Tamamlanan verification sayısı
        /// </summary>
        public int CompletedVerifications { get; set; }

        /// <summary>
        /// Sorunlu verification sayısı
        /// </summary>
        public int VerificationsWithIssues { get; set; }

        /// <summary>
        /// Tamamlanma yüzdesi
        /// </summary>
        public double CompletionPercentage { get; set; }

        /// <summary>
        /// Bu ay tamamlanan verification sayısı
        /// </summary>
        public int CompletedThisMonth { get; set; }

        /// <summary>
        /// Bu hafta tamamlanan verification sayısı
        /// </summary>
        public int CompletedThisWeek { get; set; }

        /// <summary>
        /// Aktif archivist sayısı
        /// </summary>
        public int ActiveArchivists { get; set; }

        /// <summary>
        /// Genel istatistikler
        /// </summary>
        public CourseVerificationStatisticsDto OverallStatistics { get; set; } = new();

        /// <summary>
        /// Dönem dağılımı
        /// </summary>
        public Dictionary<string, int> PeriodDistribution { get; set; } = new();

        /// <summary>
        /// Son aktiviteler
        /// </summary>
        public List<string> RecentActivity { get; set; } = new();
    }

    /// <summary>
    /// Course verification istatistikleri DTO'su
    /// </summary>
    public class CourseVerificationStatisticsDto
    {
        /// <summary>
        /// Toplam verification sayısı
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Bekleyen verification sayısı
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// Tamamlanan verification sayısı
        /// </summary>
        public int CompletedCount { get; set; }

        /// <summary>
        /// Sorunlu verification sayısı
        /// </summary>
        public int IssueCount { get; set; }

        /// <summary>
        /// Tamamlanma yüzdesi
        /// </summary>
        public double CompletionPercentage { get; set; }

        /// <summary>
        /// Ortalama tamamlanma süresi (gün)
        /// </summary>
        public double AverageCompletionDays { get; set; }

        /// <summary>
        /// En son güncelleme tarihi
        /// </summary>
        public DateTime? LastUpdated { get; set; }
    }

    /// <summary>
    /// Arama request DTO'su
    /// </summary>
    public class SearchCourseVerificationRequestDto
    {
        /// <summary>
        /// Akademisyen TC (opsiyonel)
        /// </summary>
        public string? AcademicianTc { get; set; }

        /// <summary>
        /// Ders kodu (opsiyonel)
        /// </summary>
        public string? CourseCode { get; set; }

        /// <summary>
        /// Dönem adı (opsiyonel)
        /// </summary>
        public string? Period { get; set; }

        /// <summary>
        /// Verification status (opsiyonel)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Overall verification status (opsiyonel)
        /// </summary>
        public string? OverallStatus { get; set; }

        /// <summary>
        /// Başlangıç tarihi (opsiyonel)
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Bitiş tarihi (opsiyonel)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Archivist ID (opsiyonel)
        /// </summary>
        public string? ArchivistId { get; set; }

        /// <summary>
        /// Sayfa numarası
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Sayfa boyutu
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Sıralama alanı
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// Sıralama yönü (asc/desc)
        /// </summary>
        public string? SortDirection { get; set; } = "asc";
    }

    /// <summary>
    /// Sync result DTO'su
    /// </summary>
    public class SyncResultDto
    {
        /// <summary>
        /// Sync işlemi başarılı mı
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Oluşturulan kayıt sayısı
        /// </summary>
        public int CreatedCount { get; set; }

        /// <summary>
        /// Güncellenen kayıt sayısı
        /// </summary>
        public int UpdatedCount { get; set; }

        /// <summary>
        /// Atlanan kayıt sayısı
        /// </summary>
        public int SkippedCount { get; set; }

        /// <summary>
        /// Hata mesajları
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new();

        /// <summary>
        /// Sync başlangıç zamanı
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Sync bitiş zamanı
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Sync süresi
        /// </summary>
        [JsonIgnore]
        public TimeSpan? Duration => EndTime?.Subtract(StartTime);
    }

    /// <summary>
    /// Ders bilgileri DTO - ArelBridge entegrasyonu için
    /// </summary>
    public class CourseInformationDto
    {
        /// <summary>
        /// Akademisyen TC Kimlik Numarası
        /// </summary>
        public string AcademicianTc { get; set; } = null!;

        /// <summary>
        /// Ders kodu (örn: BIL101, MAT201)
        /// </summary>
        public string CourseCode { get; set; } = null!;

        /// <summary>
        /// Dönem adı (örn: 2024-2025-Güz, 2024-2025-Bahar)
        /// </summary>
        public string PeriodName { get; set; } = null!;

        /// <summary>
        /// Ders adı (örn: Bilgisayar Programlama, Matematik I)
        /// </summary>
        public string CourseName { get; set; } = null!;

        /// <summary>
        /// Ders kredisi
        /// </summary>
        public int? Credits { get; set; }

        /// <summary>
        /// Teorik saat
        /// </summary>
        public int? TheoreticalHours { get; set; }

        /// <summary>
        /// Pratik saat
        /// </summary>
        public int? PracticalHours { get; set; }

        /// <summary>
        /// Ders türü (Zorunlu/Seçmeli)
        /// </summary>
        public string? CourseType { get; set; }

        /// <summary>
        /// Bölüm adı
        /// </summary>
        public string? DepartmentName { get; set; }

        /// <summary>
        /// Fakülte adı
        /// </summary>
        public string? FacultyName { get; set; }
    }

    /// <summary>
    /// Verification status değerleri
    /// </summary>
    public static class VerificationStatus
    {
        public const string Pending = "Pending";
        public const string VerifiedInEbys = "VerifiedInEbys";
        public const string Missing = "Missing";
        public const string DiscrepancyFound = "DiscrepancyFound";
        public const string NotApplicable = "NotApplicable";

        public static readonly string[] AllStatuses =
        {
            Pending, VerifiedInEbys, Missing, DiscrepancyFound, NotApplicable
        };

        public static bool IsValid(string status)
        {
            return AllStatuses.Contains(status);
        }
    }

    /// <summary>
    /// Overall verification status değerleri
    /// </summary>
    public static class OverallVerificationStatus
    {
        public const string Pending = "Pending";
        public const string AllVerified = "AllVerified";
        public const string PartiallyVerified = "PartiallyVerified";
        public const string HasIssues = "HasIssues";
        public const string NotApplicable = "NotApplicable";

        public static readonly string[] AllStatuses =
        {
            Pending, AllVerified, PartiallyVerified, HasIssues, NotApplicable
        };
    }

    /// <summary>
    /// Portfolio başlık türleri
    /// </summary>
    public static class PortfolioItemTypes
    {
        public const string ExamPapers = "ExamPapers";
        public const string AnswerKey = "AnswerKey";
        public const string ExamRecord = "ExamRecord";
        public const string AttendanceSheet = "AttendanceSheet";
        public const string CourseSyllabus = "CourseSyllabus";
        public const string WeeklyAttendance = "WeeklyAttendance";
        public const string MakeupExamGrades = "MakeupExamGrades";
        public const string UzemRecords = "UzemRecords";

        public static readonly string[] AllTypes =
        {
            ExamPapers, AnswerKey, ExamRecord, AttendanceSheet,
            CourseSyllabus, WeeklyAttendance, MakeupExamGrades, UzemRecords
        };

        public static readonly Dictionary<string, string> TypeDisplayNames = new()
        {
            { ExamPapers, "Sınav Kâğıtları" },
            { AnswerKey, "Yanıt Anahtarı" },
            { ExamRecord, "Sınav Tutanağı" },
            { AttendanceSheet, "Yoklama Kâğıdı" },
            { CourseSyllabus, "Ders İzlencesi" },
            { WeeklyAttendance, "14 Haftalık Ders Yoklama Listeleri" },
            { MakeupExamGrades, "Bütünleme Sınavından Alınmış Not Çizelgesi" },
            { UzemRecords, "UZEM Sistemi'nde Ders ve Sınav Yoklamaları" }
        };
    }

    /// <summary>
    /// Toplu güncelleme sonucu DTO'su
    /// </summary>
    public class BulkUpdateResultDto
    {
        /// <summary>
        /// Başarılı güncelleme sayısı
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// Başarısız güncelleme sayısı
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// Toplam işlem sayısı
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Hata mesajları
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new();

        /// <summary>
        /// İşlem başarılı mı?
        /// </summary>
        public bool IsSuccessful => FailedCount == 0;
    }

    /// <summary>
    /// Portfolio verification istatistikleri DTO'su
    /// </summary>
    public class PortfolioVerificationStatisticsDto
    {
        /// <summary>
        /// Toplam verification sayısı
        /// </summary>
        public int TotalVerifications { get; set; }

        /// <summary>
        /// Bekleyen verification sayısı
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// Tamamlanan verification sayısı
        /// </summary>
        public int CompletedCount { get; set; }

        /// <summary>
        /// Eksik belge sayısı
        /// </summary>
        public int MissingCount { get; set; }

        /// <summary>
        /// Tutarsızlık bulunan sayısı
        /// </summary>
        public int DiscrepancyCount { get; set; }

        /// <summary>
        /// Tamamlanma yüzdesi
        /// </summary>
        public double CompletionPercentage { get; set; }

        /// <summary>
        /// Dönem bazlı dağılım
        /// </summary>
        public Dictionary<string, int> DistributionByPeriod { get; set; } = new();

        /// <summary>
        /// Status bazlı dağılım
        /// </summary>
        public Dictionary<string, int> DistributionByStatus { get; set; } = new();
    }
}
