using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Bölüm performans göstergeleri DTO'su
    /// </summary>
    public class DepartmentPerformanceDto
    {
        public string Id { get; set; } = string.Empty;
        public int AutoIncrementId { get; set; }
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string FacultyId { get; set; } = string.Empty;
        public string FacultyName { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;
        public DateTime EvaluationDate { get; set; }
        
        // Performans Metrikleri
        public double OverallScore { get; set; }
        public double AcademicStaffPerformance { get; set; }
        public double ResearchPerformance { get; set; }
        public double PublicationPerformance { get; set; }
        public double StudentSatisfactionScore { get; set; }
        public double InfrastructureScore { get; set; }
        public double BudgetEfficiencyScore { get; set; }
        
        // <PERSON>statistik<PERSON>
        public int TotalAcademicStaff { get; set; }
        public int TotalStudents { get; set; }
        public int CompletedSubmissions { get; set; }
        public int PendingSubmissions { get; set; }
        public double CompletionRate { get; set; }
        
        // Metadata
        public string CreatedByUserId { get; set; } = string.Empty;
        public string CreatedByUserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string? UpdatedByUserId { get; set; }
        public string? UpdatedByUserName { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Bölüm performans oluşturma DTO'su
    /// </summary>
    public class DepartmentPerformanceCreateDto
    {
        [Required]
        [StringLength(255)]
        public string DepartmentId { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Period { get; set; } = string.Empty;

        [Required]
        public DateTime EvaluationDate { get; set; }

        [Range(0, 100)]
        public double AcademicStaffPerformance { get; set; }

        [Range(0, 100)]
        public double ResearchPerformance { get; set; }

        [Range(0, 100)]
        public double PublicationPerformance { get; set; }

        [Range(0, 100)]
        public double StudentSatisfactionScore { get; set; }

        [Range(0, 100)]
        public double InfrastructureScore { get; set; }

        [Range(0, 100)]
        public double BudgetEfficiencyScore { get; set; }

        [Range(0, int.MaxValue)]
        public int TotalAcademicStaff { get; set; }

        [Range(0, int.MaxValue)]
        public int TotalStudents { get; set; }

        public string? Notes { get; set; }
    }

    /// <summary>
    /// Bölüm performans güncelleme DTO'su
    /// </summary>
    public class DepartmentPerformanceUpdateDto : DepartmentPerformanceCreateDto
    {
        [Required]
        public string Id { get; set; } = string.Empty;
    }

    /// <summary>
    /// Bölüm performans özet DTO'su
    /// </summary>
    public class DepartmentPerformanceSummaryDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string FacultyName { get; set; } = string.Empty;
        public double CurrentOverallScore { get; set; }
        public double PreviousOverallScore { get; set; }
        public double ScoreChange { get; set; }
        public string Trend { get; set; } = string.Empty; // "Improving", "Declining", "Stable"
        public int Ranking { get; set; }
        public DateTime LastEvaluationDate { get; set; }
        public string Status { get; set; } = string.Empty; // "Excellent", "Good", "Average", "Poor"
    }

    /// <summary>
    /// Bölüm karşılaştırma DTO'su
    /// </summary>
    public class DepartmentComparisonDto
    {
        public string Period { get; set; } = string.Empty;
        public List<DepartmentPerformanceSummaryDto> Departments { get; set; } = new();
        public DepartmentBenchmarkDto Benchmark { get; set; } = new();
    }

    /// <summary>
    /// Bölüm benchmark DTO'su
    /// </summary>
    public class DepartmentBenchmarkDto
    {
        public double AverageOverallScore { get; set; }
        public double TopPerformerScore { get; set; }
        public double BottomPerformerScore { get; set; }
        public double StandardDeviation { get; set; }
        public string TopPerformingDepartment { get; set; } = string.Empty;
        public string BottomPerformingDepartment { get; set; } = string.Empty;
    }

    /// <summary>
    /// Bölüm trend analizi DTO'su
    /// </summary>
    public class DepartmentTrendAnalysisDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public List<DepartmentTrendDataDto> TrendData { get; set; } = new();
        public DepartmentTrendSummaryDto Summary { get; set; } = new();
    }

    /// <summary>
    /// Bölüm trend veri DTO'su
    /// </summary>
    public class DepartmentTrendDataDto
    {
        public string Period { get; set; } = string.Empty;
        public DateTime EvaluationDate { get; set; }
        public double OverallScore { get; set; }
        public double AcademicStaffPerformance { get; set; }
        public double ResearchPerformance { get; set; }
        public double PublicationPerformance { get; set; }
        public double StudentSatisfactionScore { get; set; }
        public int Ranking { get; set; }
    }

    /// <summary>
    /// Bölüm trend özet DTO'su
    /// </summary>
    public class DepartmentTrendSummaryDto
    {
        public double AverageGrowthRate { get; set; }
        public double BestPeriodScore { get; set; }
        public string BestPeriod { get; set; } = string.Empty;
        public double WorstPeriodScore { get; set; }
        public string WorstPeriod { get; set; } = string.Empty;
        public string OverallTrend { get; set; } = string.Empty; // "Improving", "Declining", "Stable", "Volatile"
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
    }

    /// <summary>
    /// Bölüm dashboard DTO'su
    /// </summary>
    public class DepartmentDashboardDto
    {
        public DepartmentPerformanceDto CurrentPerformance { get; set; } = new();
        public DepartmentPerformanceSummaryDto Summary { get; set; } = new();
        public List<DepartmentTrendDataDto> RecentTrends { get; set; } = new();
        public DepartmentRankingDto Ranking { get; set; } = new();
        public List<DepartmentAlertDto> Alerts { get; set; } = new();
        public DepartmentStatisticsDto Statistics { get; set; } = new();
    }

    /// <summary>
    /// Bölüm sıralama DTO'su
    /// </summary>
    public class DepartmentRankingDto
    {
        public int CurrentRanking { get; set; }
        public int TotalDepartments { get; set; }
        public int PreviousRanking { get; set; }
        public int RankingChange { get; set; }
        public string RankingTrend { get; set; } = string.Empty; // "Up", "Down", "Same"
        public double ScoreGapToTop { get; set; }
        public double ScoreGapToNext { get; set; }
    }

    /// <summary>
    /// Bölüm uyarı DTO'su
    /// </summary>
    public class DepartmentAlertDto
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "Warning", "Critical", "Info"
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // "Performance", "Deadline", "Data"
        public DateTime CreatedAt { get; set; }
        public bool IsRead { get; set; }
        public string? ActionRequired { get; set; }
    }

    /// <summary>
    /// Bölüm istatistikleri DTO'su
    /// </summary>
    public class DepartmentStatisticsDto
    {
        public int TotalEvaluations { get; set; }
        public double AverageScore { get; set; }
        public double HighestScore { get; set; }
        public double LowestScore { get; set; }
        public int ConsecutiveImprovements { get; set; }
        public int ConsecutiveDeclines { get; set; }
        public double YearOverYearGrowth { get; set; }
        public Dictionary<string, double> CategoryAverages { get; set; } = new();
        public Dictionary<string, int> MonthlySubmissions { get; set; } = new();
    }
}
