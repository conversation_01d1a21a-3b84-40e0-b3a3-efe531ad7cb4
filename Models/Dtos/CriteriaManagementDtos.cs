using AcademicPerformance.Models.MongoDocuments;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Models.Dtos
{
    // Dynamic Criteria DTOs
    public class DynamicCriterionTemplateDto
    {
        public string? Id { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public required string Status { get; set; } // Draft, Active, Inactive
        public List<DynamicInputFieldDto> InputFields { get; set; } = new();
        public List<ValidationRuleDto>? ValidationRules { get; set; }
        public string? CalculationLogic { get; set; }
        public double? Coefficient { get; set; }
        public double? MaximumLimit { get; set; }
        public List<string>? ApplicableAcademicCadres { get; set; }
        public string? CreatedByUserId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string? UpdatedByUserId { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class DynamicCriterionTemplateCreateDto
    {
        public required string Name { get; set; }
        public string? Description { get; set; }
        public List<DynamicInputFieldDto> InputFields { get; set; } = new();
        public List<ValidationRuleDto>? ValidationRules { get; set; }
        public string? CalculationLogic { get; set; }
        public double? Coefficient { get; set; }
        public double? MaximumLimit { get; set; }
        public List<string>? ApplicableAcademicCadres { get; set; }
    }

    public class DynamicCriterionTemplateUpdateDto
    {
        public required string Id { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public List<DynamicInputFieldDto> InputFields { get; set; } = new();
        public List<ValidationRuleDto>? ValidationRules { get; set; }
        public string? CalculationLogic { get; set; }
        public double? Coefficient { get; set; }
        public double? MaximumLimit { get; set; }
        public List<string>? ApplicableAcademicCadres { get; set; }
    }

    public class DynamicInputFieldDto
    {
        public required string FieldId { get; set; }
        public required string FieldName { get; set; }
        public required string FieldType { get; set; } // Text, Number, Date, File, Select
        public bool IsRequired { get; set; }
        public int DisplayOrder { get; set; }
        public string? PlaceholderText { get; set; }
        public string? HelpText { get; set; }
        public FieldConstraintsDto? Constraints { get; set; }
        public List<FieldOptionDto>? Options { get; set; }
    }

    public class FieldConstraintsDto
    {
        public int? MaxLength { get; set; }
        public int? MinLength { get; set; }
        public double? MinValue { get; set; }
        public double? MaxValue { get; set; }
        public DateTime? MinDate { get; set; }
        public DateTime? MaxDate { get; set; }
        public List<string>? AllowedFileTypes { get; set; }
        public long? MaxFileSize { get; set; }
        public string? Pattern { get; set; }
    }

    public class FieldOptionDto
    {
        public required string Value { get; set; }
        public required string Label { get; set; }
        public bool IsDefault { get; set; }
        public int DisplayOrder { get; set; }
    }

    public class ValidationRuleDto
    {
        public required string RuleType { get; set; }
        public required string RuleExpression { get; set; }
        public required string ErrorMessage { get; set; }
    }

    // Static Criteria DTOs
    public class StaticCriterionDefinitionDto
    {
        public required string StaticCriterionSystemId { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public required string DataType { get; set; }
        public string? DataSourceHint { get; set; }
        public string? CalculationLogic { get; set; }
        public bool IsActive { get; set; }
        public List<RlxLocalizationDto> Localizations { get; set; } = new();
    }

    public class StaticCriterionDefinitionUpdateDto
    {
        public required string StaticCriterionSystemId { get; set; }
        public bool IsActive { get; set; }
    }

    // Common DTOs
    public class CriterionStatusUpdateDto
    {
        public required string Id { get; set; }
        public required string Status { get; set; }
    }
}
