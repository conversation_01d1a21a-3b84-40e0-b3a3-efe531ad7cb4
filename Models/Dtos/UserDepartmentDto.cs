namespace AcademicPerformance.Models.Dtos
{
    public class UserDepartmentDto
    {
        public string DepartmentId { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public bool IsPrimary { get; set; } = false;
        public string PositiomName { get; set; } = string.Empty;
        public string PositionId { get; set; } = string.Empty;
        public string PositionCode { get; set; } = string.Empty;
        public string UnitName { get; set; } = string.Empty;
        public string UnitId { get; set; } = string.Empty;
        public string UnitCode { get; set; } = string.Empty;
        public string UnitTypeName { get; set; } = string.Empty;
        public string UnitTypeId { get; set; } = string.Empty;
        public string UnitTypeCode { get; set; } = string.Empty;
    }
}
