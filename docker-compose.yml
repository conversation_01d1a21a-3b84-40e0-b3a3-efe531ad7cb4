services:
    postgres:
        image: postgres:15
        container_name: apdys-postgres
        environment:
            POSTGRES_USER: ${POSTGRES_USER}
            POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
            POSTGRES_DB: postgres
        ports:
            - "5432:5432"
        volumes:
            - postgres_data:/var/lib/postgresql/data
        networks:
            - apdys-network
        healthcheck:
            test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
            interval: 30s
            timeout: 10s
            retries: 3

    mongodb:
        image: mongo:7
        container_name: apdys-mongodb
        environment:
            MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
            MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
            MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
        ports:
            - "27017:27017"
        volumes:
            - mongodb_data:/data/db
            - ./init-scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
        networks:
            - apdys-network
        healthcheck:
            test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
            interval: 30s
            timeout: 10s
            retries: 3

    redis:
        image: redis:7-alpine
        container_name: apdys-redis
        ports:
            - "6379:6379"
        volumes:
            - redis_data:/data
        networks:
            - apdys-network
        healthcheck:
            test: ["CMD", "redis-cli", "ping"]
            interval: 30s
            timeout: 10s
            retries: 3

    minio:
        image: quay.io/minio/minio:latest
        container_name: apdys-minio
        ports:
            - "9000:9000" # API Port
            - "9001:9001" # Console Port
        environment:
            MINIO_ROOT_USER: ${MINIO_ROOT_USER}
            MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
            MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
        volumes:
            - minio_data:/data
            - minio_config:/root/.minio
        command: server /data --console-address ":9001"
        networks:
            - apdys-network
        healthcheck:
            test:
                ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
            interval: 30s
            timeout: 20s
            retries: 3

    academicperformance:
        build:
            context: ..
            dockerfile: AcademicPerformance/Dockerfile
        container_name: apdys-app
        environment:
            - ASPNETCORE_ENVIRONMENT=Docker
            - ConnectionStrings__OrganizationManagement=Host=postgres;Database=organizationmanagement;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
            - ConnectionStrings__RlxIdentityShared=Host=postgres;Database=rlxidentity;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
            - ConnectionStrings__AcademicPerformance=Host=postgres;Database=academicperformance;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
            - ConnectionStrings__ApdysMongoDb=mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@mongodb:27017/${MONGO_DATABASE}?authSource=admin
            - RedisCache__ConnectionString=redis:6379
            - OpenIddict__Issuer=${OPENIDDICT_ISSUER}
            - MinIO__Endpoint=minio:9000
            - MinIO__AccessKey=${MINIO_ROOT_USER}
            - MinIO__SecretKey=${MINIO_ROOT_PASSWORD}
            - MinIO__UseSSL=false
            - MinIO__DefaultBucket=apdys-evidence-files
        ports:
            - "5122:8080"
        depends_on:
            postgres:
                condition: service_healthy
            mongodb:
                condition: service_healthy
            redis:
                condition: service_healthy
            minio:
                condition: service_healthy
        networks:
            - apdys-network
        restart: unless-stopped

volumes:
    postgres_data:
        driver: local
    mongodb_data:
        driver: local
    redis_data:
        driver: local
    minio_data:
        driver: local
    minio_config:
        driver: local

networks:
    apdys-network:
        driver: bridge
