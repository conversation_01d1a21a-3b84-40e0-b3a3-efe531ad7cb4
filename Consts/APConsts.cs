namespace AcademicPerformance.Consts;

/// <summary>
/// AcademicPerformance projesi için authorization constant'ları
/// Claim-based authorization sisteminde kullanılır
/// </summary>
public static class APConsts
{
    #region Base Permission Types
    /// <summary>
    /// Tüm sayfa erişim izinleri için genel claim type
    /// </summary>
    public const string PermissionPageAll = "permission.page.all";

    /// <summary>
    /// Tüm action izinleri için genel claim type
    /// </summary>
    public const string PermissionActionAll = "permission.action.all";

    /// <summary>
    /// AcademicPerformance spesifik action izinleri için claim type
    /// </summary>
    public const string PermissionActionAP = "permission.action.ap";

    /// <summary>
    /// AcademicPerformance spesifik sayfa erişim izinleri için claim type
    /// </summary>
    public const string PermissionPageAP = "permission.page.ap";
    #endregion

    /// <summary>
    /// Controller'larda kullanılacak policy string'leri
    /// Format: "permission.action.ap.{actionName}"
    /// </summary>
    public static class Policies
    {
        /// <summary>
        /// Kriter şablonları yönetimi policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ManageCriteria)]
        /// </summary>
        public const string ManageCriteria = "permission.action.ap.managecriteria";

        /// <summary>
        /// Performans verisi gönderme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.SubmitData)]
        /// </summary>
        public const string SubmitData = "permission.action.ap.submitdata";

        /// <summary>
        /// Gönderileri onaylama/reddetme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ApproveSubmissions)]
        /// </summary>
        public const string ApproveSubmissions = "permission.action.ap.approvesubmissions";

        /// <summary>
        /// Değerlendirme formları yönetimi policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ManageForms)]
        /// </summary>
        public const string ManageForms = "permission.action.ap.manageforms";

        /// <summary>
        /// Raporları görüntüleme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ViewReports)]
        /// </summary>
        public const string ViewReports = "permission.action.ap.viewreports";

        /// <summary>
        /// Bölüm performans verisi girme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.InputDepartmentData)]
        /// </summary>
        public const string InputDepartmentData = "permission.action.ap.inputdepartmentdata";

        /// <summary>
        /// Personel yetkinlik değerlendirme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.EvaluateStaff)]
        /// </summary>
        public const string EvaluateStaff = "permission.action.ap.evaluatestaff";

        /// <summary>
        /// Portfolio öğelerini doğrulama policy'si
        /// Kullanım: [Authorize(APConsts.Policies.VerifyPortfolio)]
        /// </summary>
        public const string VerifyPortfolio = "permission.action.ap.verifyportfolio";

        /// <summary>
        /// AcademicPerformance sistem erişimi policy'si
        /// Kullanım: [Authorize(APConsts.Policies.AccessAP)]
        /// </summary>
        public const string AccessAP = "permission.page.ap.ap";

        /// <summary>
        /// Test/development amaçlı policy
        /// Kullanım: [Authorize(APConsts.Policies.Test)]
        /// </summary>
        public const string Test = "permission.action.ap.test";

        /// <summary>
        /// Genel erişim policy'si (tüm authenticated kullanıcılar)
        /// Kullanım: [Authorize(APConsts.Policies.AllAccess)]
        /// </summary>
        public const string AllAccess = "permission.action.ap.allaccess";

        /// <summary>
        /// Statik kriter verilerini görüntüleme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ViewStaticData)]
        /// </summary>
        public const string ViewStaticData = "permission.action.ap.viewstaticdata";

        /// <summary>
        /// Raporlama dashboard'ına erişim policy'si
        /// Kullanım: [Authorize(APConsts.Policies.AccessReporting)]
        /// </summary>
        public const string AccessReporting = "permission.action.ap.accessreporting";

        /// <summary>
        /// Admin rolü gerektiren işlemler policy'si
        /// Kullanım: [Authorize(APConsts.Policies.RequireAdminRole)]
        /// </summary>
        public const string RequireAdminRole = "permission.action.ap.requireadminrole";

        /// <summary>
        /// Dosya yükleme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.UploadFiles)]
        /// </summary>
        public const string UploadFiles = "permission.action.ap.uploadfiles";

        /// <summary>
        /// Dosya indirme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.DownloadFiles)]
        /// </summary>
        public const string DownloadFiles = "permission.action.ap.downloadfiles";

        /// <summary>
        /// Dosya silme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.DeleteFiles)]
        /// </summary>
        public const string DeleteFiles = "permission.action.ap.deletefiles";

        /// <summary>
        /// Dosya yönetimi (tüm file operations) policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ManageFiles)]
        /// </summary>
        public const string ManageFiles = "permission.action.ap.managefiles";


        /// <summary>
        /// Submission'ları review etme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ReviewSubmissions)]
        /// </summary>
        public const string ReviewSubmissions = "permission.action.ap.reviewsubmissions";

        /// <summary>
        /// Controller dashboard'ına erişim policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ViewControllerDashboard)]
        /// </summary>
        public const string ViewControllerDashboard = "permission.action.ap.viewcontrollerdashboard";

        /// <summary>
        /// Submission workflow yönetimi policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ManageSubmissionWorkflow)]
        /// </summary>
        public const string ManageSubmissionWorkflow = "permission.action.ap.managesubmissionworkflow";

        /// <summary>
        /// Submission detaylarına erişim policy'si
        /// Kullanım: [Authorize(APConsts.Policies.AccessSubmissionDetails)]
        /// </summary>
        public const string AccessSubmissionDetails = "permission.action.ap.accesssubmissiondetails";

        /// <summary>
        /// Evidence file'ları indirme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.DownloadEvidenceFiles)]
        /// </summary>
        public const string DownloadEvidenceFiles = "permission.action.ap.downloadevidencefiles";

        /// <summary>
        /// Submission'ları controller'lara atama policy'si
        /// Kullanım: [Authorize(APConsts.Policies.AssignSubmissions)]
        /// </summary>
        public const string AssignSubmissions = "permission.action.ap.assignsubmissions";

        /// <summary>
        /// Audit trail görüntüleme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ViewAuditTrail)]
        /// </summary>
        public const string ViewAuditTrail = "permission.action.ap.viewaudittrail";

        /// <summary>
        /// Controller ayarları yönetimi policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ManageControllerSettings)]
        /// </summary>
        public const string ManageControllerSettings = "permission.action.ap.managecontrollersettings";

        /// <summary>
        /// Veri görüntüleme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ViewData)]
        /// </summary>
        public const string ViewData = "permission.action.ap.viewdata";

        /// <summary>
        /// Submission'ları görüntüleme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.ViewSubmissions)]
        /// </summary>
        public const string ViewSubmissions = "permission.action.ap.viewsubmissions";

        /// <summary>
        /// Veri düzenleme policy'si
        /// Kullanım: [Authorize(APConsts.Policies.EditData)]
        /// </summary>
        public const string EditData = "permission.action.ap.editdata";
    }

}