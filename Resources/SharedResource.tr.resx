<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string"/>
              <xsd:attribute name="type" type="xsd:string"/>
              <xsd:attribute name="mimetype" type="xsd:string"/>
              <xsd:attribute ref="xml:space"/>
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string"/>
              <xsd:attribute name="name" type="xsd:string"/>
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
                <xsd:element name="comment" type="xsd:string" minOccurs="0"/>
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string"/>
              <xsd:attribute name="type" type="xsd:string"/>
              <xsd:attribute name="mimetype" type="xsd:string"/>
              <xsd:attribute ref="xml:space"/>
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string"/>
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>Test TR</value>
    <comment/>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Akademik Performans Sistemine Hoş Geldiniz</value>
    <comment/>
  </data>
  <data name="Success" xml:space="preserve">
    <value>İşlem başarıyla tamamlandı</value>
    <comment/>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Bir hata oluştu</value>
    <comment/>
  </data>
  <data name="InternalServerError" xml:space="preserve">
    <value>İç sunucu hatası oluştu</value>
    <comment/>
  </data>
  <data name="DynamicCriterionTemplateNotFound" xml:space="preserve">
    <value>Dinamik kriter şablonu bulunamadı</value>
    <comment/>
  </data>
  <data name="UserIdNotFoundInToken" xml:space="preserve">
    <value>Token'da kullanıcı ID'si bulunamadı</value>
    <comment/>
  </data>
  <data name="IdMismatch" xml:space="preserve">
    <value>ID uyuşmazlığı</value>
    <comment/>
  </data>
  <data name="StaticCriterionDefinitionNotFound" xml:space="preserve">
    <value>Statik kriter tanımı bulunamadı</value>
    <comment/>
  </data>
  <data name="DynamicCriterionTemplateUpdatedSuccessfully" xml:space="preserve">
    <value>Dinamik kriter şablonu başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="DynamicCriterionTemplateStatusUpdatedSuccessfully" xml:space="preserve">
    <value>Dinamik kriter şablonu durumu başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="DynamicCriterionTemplateDeletedSuccessfully" xml:space="preserve">
    <value>Dinamik kriter şablonu başarıyla silindi</value>
    <comment/>
  </data>
  <data name="StaticCriterionDefinitionStatusUpdatedSuccessfully" xml:space="preserve">
    <value>Statik kriter tanımı durumu başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="ResourceCreatedSuccessfully" xml:space="preserve">
    <value>Kaynak başarıyla oluşturuldu</value>
    <comment/>
  </data>
  <data name="ResourceNotFound" xml:space="preserve">
    <value>Kaynak bulunamadı</value>
    <comment/>
  </data>
  <data name="UnauthorizedAccess" xml:space="preserve">
    <value>Yetkisiz erişim</value>
    <comment/>
  </data>
  <data name="AnInternalServerErrorOccurred" xml:space="preserve">
    <value>İç sunucu hatası oluştu</value>
    <comment/>
  </data>
  <data name="EvaluationFormNotFound" xml:space="preserve">
    <value>Değerlendirme formu bulunamadı</value>
    <comment/>
  </data>
  <data name="EvaluationFormUpdatedSuccessfully" xml:space="preserve">
    <value>Değerlendirme formu başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="EvaluationFormStatusUpdatedSuccessfully" xml:space="preserve">
    <value>Değerlendirme formu durumu başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="EvaluationFormDeletedSuccessfully" xml:space="preserve">
    <value>Değerlendirme formu başarıyla silindi</value>
    <comment/>
  </data>
  <data name="FormCategoryNotFound" xml:space="preserve">
    <value>Form kategorisi bulunamadı</value>
    <comment/>
  </data>
  <data name="FormCategoryUpdatedSuccessfully" xml:space="preserve">
    <value>Form kategorisi başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="FormCategoryDeletedSuccessfully" xml:space="preserve">
    <value>Form kategorisi başarıyla silindi</value>
    <comment/>
  </data>
  <data name="CategoryWeightsAreValid" xml:space="preserve">
    <value>Kategori ağırlıkları geçerli</value>
    <comment/>
  </data>
  <data name="CategoryWeightsDoNotSumTo100" xml:space="preserve">
    <value>Kategori ağırlıkları %100'e eşit değil</value>
    <comment/>
  </data>
  <data name="FormCriterionLinkNotFound" xml:space="preserve">
    <value>Form kriter bağlantısı bulunamadı</value>
    <comment/>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>Doğrulama hatası</value>
    <comment/>
  </data>
  <data name="RequiredField" xml:space="preserve">
    <value>Bu alan zorunludur</value>
    <comment/>
  </data>
  <data name="InvalidFormat" xml:space="preserve">
    <value>Geçersiz format</value>
    <comment/>
  </data>
  <data name="InvalidValue" xml:space="preserve">
    <value>Geçersiz değer</value>
    <comment/>
  </data>
  <data name="FormCriterionLinkUpdatedSuccessfully" xml:space="preserve">
    <value>Form kriter bağlantısı başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="FormCriterionLinkDeletedSuccessfully" xml:space="preserve">
    <value>Form kriter bağlantısı başarıyla silindi</value>
    <comment/>
  </data>
  <data name="EvaluationFormRetrievedSuccessfully" xml:space="preserve">
    <value>Değerlendirme formu başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="FormNotFoundOrAccessDenied" xml:space="preserve">
    <value>Form bulunamadı veya erişim reddedildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingEvaluationForm" xml:space="preserve">
    <value>Değerlendirme formu getirilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="FormCategoriesRetrievedSuccessfully" xml:space="preserve">
    <value>Form kategorileri başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingFormCategories" xml:space="preserve">
    <value>Form kategorileri getirilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="FormCriterionLinksRetrievedSuccessfully" xml:space="preserve">
    <value>Form kriter bağlantıları başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="CategoryNotFoundOrAccessDenied" xml:space="preserve">
    <value>Kategori bulunamadı veya erişim reddedildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingFormCriterionLinks" xml:space="preserve">
    <value>Form kriter bağlantıları getirilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="SubmissionNotFound" xml:space="preserve">
    <value>Değerlendirme başvurusu bulunamadı</value>
    <comment/>
  </data>
  <data name="SubmissionRetrievedSuccessfully" xml:space="preserve">
    <value>Değerlendirme başvurusu başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingSubmission" xml:space="preserve">
    <value>Değerlendirme başvurusu getirilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="SubmissionCreatedSuccessfully" xml:space="preserve">
    <value>Değerlendirme başvurusu başarıyla oluşturuldu</value>
    <comment/>
  </data>
  <data name="ErrorCreatingSubmission" xml:space="preserve">
    <value>Değerlendirme başvurusu oluşturulurken hata oluştu</value>
    <comment/>
  </data>
  <data name="SubmissionNotFoundOrCannotUpdate" xml:space="preserve">
    <value>Değerlendirme başvurusu bulunamadı veya güncellenemez</value>
    <comment/>
  </data>
  <data name="SubmissionUpdatedSuccessfully" xml:space="preserve">
    <value>Değerlendirme başvurusu başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="ErrorUpdatingSubmission" xml:space="preserve">
    <value>Değerlendirme başvurusu güncellenirken hata oluştu</value>
    <comment/>
  </data>
  <data name="ErrorInputtingCriterionData" xml:space="preserve">
    <value>Kriter verisi girilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="CriterionDataInputSuccessfully" xml:space="preserve">
    <value>Kriter verisi başarıyla girildi</value>
    <comment/>
  </data>
  <data name="CriterionDataNotFoundOrCannotUpdate" xml:space="preserve">
    <value>Kriter verisi bulunamadı veya güncellenemez</value>
    <comment/>
  </data>
  <data name="CriterionDataUpdatedSuccessfully" xml:space="preserve">
    <value>Kriter verisi başarıyla güncellendi</value>
    <comment/>
  </data>
  <data name="ErrorUpdatingCriterionData" xml:space="preserve">
    <value>Kriter verisi güncellenirken hata oluştu</value>
    <comment/>
  </data>
  <data name="CriterionDataNotFound" xml:space="preserve">
    <value>Kriter verisi bulunamadı</value>
    <comment/>
  </data>
  <data name="CriterionDataDeletedSuccessfully" xml:space="preserve">
    <value>Kriter verisi başarıyla silindi</value>
    <comment/>
  </data>
  <data name="ErrorDeletingCriterionData" xml:space="preserve">
    <value>Kriter verisi silinirken hata oluştu</value>
    <comment/>
  </data>
  <data name="CriterionDataEntriesRetrievedSuccessfully" xml:space="preserve">
    <value>Kriter veri girişleri başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingCriterionDataEntries" xml:space="preserve">
    <value>Kriter veri girişleri getirilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="DashboardRetrievedSuccessfully" xml:space="preserve">
    <value>Dashboard başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingDashboard" xml:space="preserve">
    <value>Dashboard getirilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="StatisticsRetrievedSuccessfully" xml:space="preserve">
    <value>İstatistikler başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingStatistics" xml:space="preserve">
    <value>İstatistikler getirilirken hata oluştu</value>
    <comment/>
  </data>
  <data name="PendingSubmissionsRetrievedSuccessfully" xml:space="preserve">
    <value>Bekleyen gönderimler başarıyla getirildi</value>
    <comment/>
  </data>
  <data name="ErrorRetrievingPendingSubmissions" xml:space="preserve">
    <value>Bekleyen gönderimler getirilirken hata oluştu</value>
    <comment/>
  </data>
</root>
