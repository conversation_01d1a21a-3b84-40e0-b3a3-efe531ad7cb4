using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Consts;
using AcademicPerformance.Services;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
namespace AcademicPerformance.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class FormController : BaseApiController
    {
        private readonly IFormManager _formManager;
        private readonly IUserContextHelper _userContextHelper;
        private readonly IRlxSystemLogHelper<FormController> _systemLogHelper;
        private readonly ILogger<FormController> _logger;
        private readonly IAPAuthorizationService _authorizationService;
        public FormController(
            IFormManager formManager,
            IUserContextHelper userContextHelper,
            IRlxSystemLogHelper<FormController> systemLogHelper,
            ILogger<FormController> logger,
            IAPAuthorizationService authorizationService,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _formManager = formManager;
            _userContextHelper = userContextHelper;
            _systemLogHelper = systemLogHelper;
            _logger = logger;
            _authorizationService = authorizationService;
        }
        #region Evaluation Form Management
        /// <summary>
        /// Tüm değerlendirme formlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış form listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewReports)] // Form listesini görüntüleme yetkisi
        public async Task<IActionResult> GetForms([FromQuery] PagedListCo<GetEvaluationFormsCo> co)
        {
            try
            {
                var pagedForms = await _formManager.GetEvaluationFormsAsync(co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedForms.Count} evaluation forms (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedForms, _localizer["EvaluationFormsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving evaluation forms", ex);
                _logger.LogError(ex, "Error retrieving evaluation forms");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// ID'ye göre değerlendirme formunu getir (Admin: tam detaylar, Akademisyen: erişim kontrolü ile sınırlı detaylar)
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> GetForm(string id)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                // Policy-based authorization ile admin kontrolü
                var isAdmin = _authorizationService.IsAdmin(User);
                if (isAdmin)
                {
                    // Admin: Tam form detayları
                    var form = await _formManager.GetEvaluationFormByIdAsync(id);
                    if (form == null)
                    {
                        return NotFoundResponse(_localizer["EvaluationFormNotFound"].Value);
                    }
                    await _systemLogHelper.LogInfoAsync($"Admin {userId} retrieved evaluation form: {id}");
                    return SuccessResponse(form, _localizer["EvaluationFormRetrievedSuccessfully"].Value);
                }
                else
                {
                    // Akademisyen: Erişim kontrolü ile sınırlı detaylar
                    var form = await _formManager.GetEvaluationFormForAcademicianAsync(userId, id);
                    if (form == null)
                    {
                        return NotFoundResponse(_localizer["FormNotFoundOrAccessDenied"].Value);
                    }
                    await _systemLogHelper.LogInfoAsync($"Academician {userId} retrieved evaluation form: {id}");
                    return SuccessResponse(form, _localizer["EvaluationFormRetrievedSuccessfully"].Value);
                }
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving evaluation form: {id}", ex);
                _logger.LogError(ex, "Error retrieving evaluation form: {Id}", id);
                return HandleException(ex, _localizer["ErrorRetrievingEvaluationForm"].Value);
            }
        }
        /// <summary>
        /// Duruma göre değerlendirme formlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="status">Form durumu (Draft, Active, Archived)</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış form listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ManageForms)]
        public async Task<IActionResult> GetFormsByStatus(string status, [FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var pagedForms = await _formManager.GetEvaluationFormsByStatusAsync(status, co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedForms.Count} evaluation forms with status: {status} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedForms, _localizer["EvaluationFormsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving evaluation forms by status: {status}", ex);
                _logger.LogError(ex, "Error retrieving evaluation forms by status: {Status}", status);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// <summary>
        /// Sayfalanmış değerlendirme formları arama - Gelişmiş filtreleme ile
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> SearchForms([FromBody] PagedListCo<GetEvaluationFormsCo> co)
        {
            try
            {
                var pagedForms = await _formManager.GetEvaluationFormsAsync(co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedForms.Count} evaluation forms (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedForms, _localizer["EvaluationFormsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error searching evaluation forms", ex);
                _logger.LogError(ex, "Error searching evaluation forms");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// <summary>
        /// Duruma göre sayfalanmış değerlendirme formları getir
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> SearchFormsByStatus(string status, [FromBody] PagedListCo<GetEvaluationFormsCo> co)
        {
            try
            {
                var pagedForms = await _formManager.GetEvaluationFormsByStatusAsync(co, status);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedForms.Count} evaluation forms with status: {status} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedForms, _localizer["EvaluationFormsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error searching evaluation forms by status: {status}", ex);
                _logger.LogError(ex, "Error searching evaluation forms by status: {Status}", status);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Yeni bir değerlendirme formu oluştur
        [HttpPost]
        [Authorize(APConsts.Policies.ManageForms)] // Form yönetimi yetkisi
        public async Task<IActionResult> AddForm([FromBody] EvaluationFormCreateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                var createdForm = await _formManager.CreateEvaluationFormAsync(dto, userId);
                await _systemLogHelper.LogInfoAsync($"Created evaluation form: {createdForm.Id}");
                return SuccessResponse(createdForm, _localizer["EvaluationFormCreatedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error creating evaluation form", ex);
                _logger.LogError(ex, "Error creating evaluation form");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir değerlendirme formunu güncelle
        [HttpPut]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> UpdateForm(string id, [FromBody] EvaluationFormUpdateDto dto)
        {
            try
            {
                if (id != dto.Id)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var userId = _userContextHelper.GetUserId()!;
                var success = await _formManager.UpdateEvaluationFormAsync(dto, userId);
                if (!success)
                {
                    return NotFoundResponse(_localizer["EvaluationFormNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Updated evaluation form: {id}");
                return SuccessResponse(_localizer["EvaluationFormUpdatedSuccessfully"].Value);
            }
            catch (InvalidOperationException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Cannot update evaluation form {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating evaluation form: {id}", ex);
                _logger.LogError(ex, "Error updating evaluation form: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Değerlendirme formu durumunu güncelle
        [HttpPatch]
        [Authorize(APConsts.Policies.ManageForms)] // Form yönetimi yetkisi
        public async Task<IActionResult> UpdateFormStatus(string id, [FromBody] EvaluationFormStatusUpdateDto dto)
        {
            try
            {
                if (id != dto.Id)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var userId = _userContextHelper.GetUserId()!;
                var success = await _formManager.UpdateEvaluationFormStatusAsync(dto, userId);
                if (!success)
                {
                    return NotFoundResponse(_localizer["EvaluationFormNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Updated evaluation form status: {id} -> {dto.Status}");
                return SuccessResponse(_localizer["EvaluationFormStatusUpdatedSuccessfully"].Value);
            }
            catch (ArgumentException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Invalid status for evaluation form {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating evaluation form status: {id}", ex);
                _logger.LogError(ex, "Error updating evaluation form status: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir değerlendirme formunu sil
        [HttpDelete("{id}")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> DeleteForm(string id)
        {
            try
            {
                var success = await _formManager.DeleteEvaluationFormAsync(id);
                if (!success)
                {
                    return NotFoundResponse(_localizer["EvaluationFormNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Deleted evaluation form: {id}");
                return SuccessResponse(_localizer["EvaluationFormDeletedSuccessfully"].Value);
            }
            catch (InvalidOperationException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Cannot delete evaluation form {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error deleting evaluation form: {id}", ex);
                _logger.LogError(ex, "Error deleting evaluation form: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        #endregion
        #region Form Category Management
        /// Değerlendirme formu ID'sine göre form kategorilerini getir (Admin: tam detaylar, Akademisyen: erişim kontrolü ile sınırlı detaylar)
        /// <summary>
        /// Form kategorilerini sayfalanmış olarak getir
        /// </summary>
        /// <param name="formId">Form ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış form kategorileri listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> GetCategories(string formId, [FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                // Policy-based authorization ile admin kontrolü
                var isAdmin = _authorizationService.IsAdmin(User);
                if (isAdmin)
                {
                    // Admin: Tam kategori detayları
                    var pagedCategories = await _formManager.GetFormCategoriesByFormIdAsync(formId, co);
                    await _systemLogHelper.LogInfoAsync($"Admin {userId} retrieved {pagedCategories.Count} categories for form: {formId} (page {co.Pager.Page}, size {co.Pager.Size})");
                    return SuccessResponse(pagedCategories, _localizer["FormCategoriesRetrievedSuccessfully"].Value);
                }
                else
                {
                    // Akademisyen: Erişim kontrolü ile sınırlı detaylar
                    var pagedCategories = await _formManager.GetFormCategoriesForAcademicianAsync(userId, formId, co);
                    if (pagedCategories == null)
                    {
                        return NotFoundResponse(_localizer["FormNotFoundOrAccessDenied"].Value);
                    }
                    await _systemLogHelper.LogInfoAsync($"Academician {userId} retrieved {pagedCategories.Count} categories for form: {formId} (page {co.Pager.Page}, size {co.Pager.Size})");
                    return SuccessResponse(pagedCategories, _localizer["FormCategoriesRetrievedSuccessfully"].Value);
                }
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving categories for form: {formId}", ex);
                _logger.LogError(ex, "Error retrieving categories for form: {FormId}", formId);
                return HandleException(ex, _localizer["ErrorRetrievingFormCategories"].Value);
            }
        }
        /// ID'ye göre form kategorisini getir
        [HttpGet("{id}")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> GetCategory(string id)
        {
            try
            {
                var category = await _formManager.GetFormCategoryByIdAsync(id);
                if (category == null)
                {
                    return NotFoundResponse(_localizer["FormCategoryNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Retrieved form category: {id}");
                return SuccessResponse(category);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving form category: {id}", ex);
                _logger.LogError(ex, "Error retrieving form category: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Yeni bir form kategorisi oluştur
        [HttpPost]
        [Authorize(APConsts.Policies.ManageForms)] // Form yönetimi yetkisi
        public async Task<IActionResult> AddCategory([FromBody] FormCategoryCreateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                var createdCategory = await _formManager.CreateFormCategoryAsync(dto, userId);
                await _systemLogHelper.LogInfoAsync($"Created form category: {createdCategory.Id}");
                return SuccessResponse(createdCategory, _localizer["FormCategoryCreatedSuccessfully"].Value);
            }
            catch (ArgumentException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Invalid form category data: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error creating form category", ex);
                _logger.LogError(ex, "Error creating form category");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir form kategorisini güncelle
        [HttpPut("{id}")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> UpdateCategory(string id, [FromBody] FormCategoryUpdateDto dto)
        {
            try
            {
                if (id != dto.Id)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var userId = _userContextHelper.GetUserId()!;
                var success = await _formManager.UpdateFormCategoryAsync(dto, userId);
                if (!success)
                {
                    return NotFoundResponse(_localizer["FormCategoryNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Updated form category: {id}");
                return SuccessResponse(_localizer["FormCategoryUpdatedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating form category: {id}", ex);
                _logger.LogError(ex, "Error updating form category: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir form kategorisini sil
        [HttpDelete("{id}")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> DeleteCategory(string id)
        {
            try
            {
                var success = await _formManager.DeleteFormCategoryAsync(id);
                if (!success)
                {
                    return NotFoundResponse(_localizer["FormCategoryNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Deleted form category: {id}");
                return SuccessResponse(_localizer["FormCategoryDeletedSuccessfully"].Value);
            }
            catch (InvalidOperationException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Cannot delete form category {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error deleting form category: {id}", ex);
                _logger.LogError(ex, "Error deleting form category: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Form kategori ağırlıklarını doğrula
        [HttpPost]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> ValidateFormCategoryWeights([FromBody] FormCategoryWeightValidationDto dto)
        {
            try
            {
                var isValid = await _formManager.ValidateFormCategoryWeightsAsync(dto);
                await _systemLogHelper.LogInfoAsync($"Validated category weights for form: {dto.EvaluationFormId}, valid: {isValid}");
                var result = new { isValid, message = isValid ? _localizer["CategoryWeightsAreValid"].Value : _localizer["CategoryWeightsDoNotSumTo100"].Value };
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error validating category weights for form: {dto.EvaluationFormId}", ex);
                _logger.LogError(ex, "Error validating category weights for form: {FormId}", dto.EvaluationFormId);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        #endregion
        #region Form Criterion Link Management
        /// Kategori ID'sine göre form kriter bağlantılarını getir (Admin: tam detaylar, Akademisyen: erişim kontrolü ile sınırlı detaylar)
        /// <summary>
        /// Kategori kriterlerini sayfalanmış olarak getir
        /// </summary>
        /// <param name="categoryId">Kategori ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış kriter bağlantıları listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> GetFormCriterionLinks(string categoryId, [FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse(_localizer["UserIdNotFoundInToken"].Value);
                }
                // Model validation kontrolü
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }
                // Policy-based authorization ile admin kontrolü
                var isAdmin = _authorizationService.IsAdmin(User);
                if (isAdmin)
                {
                    // Admin: Tam kriter bağlantı detayları
                    var pagedLinks = await _formManager.GetFormCriterionLinksByCategoryIdAsync(categoryId, co);
                    await _systemLogHelper.LogInfoAsync($"Admin {userId} retrieved {pagedLinks.Count} criterion links for category: {categoryId} (page {co.Pager.Page}, size {co.Pager.Size})");
                    return SuccessResponse(pagedLinks, _localizer["FormCriterionLinksRetrievedSuccessfully"].Value);
                }
                else
                {
                    // Akademisyen: Erişim kontrolü ile sınırlı detaylar
                    var pagedLinks = await _formManager.GetFormCriterionLinksForAcademicianAsync(userId, categoryId, co);
                    if (pagedLinks == null)
                    {
                        return NotFoundResponse(_localizer["CategoryNotFoundOrAccessDenied"].Value);
                    }
                    await _systemLogHelper.LogInfoAsync($"Academician {userId} retrieved {pagedLinks.Count} criterion links for category: {categoryId} (page {co.Pager.Page}, size {co.Pager.Size})");
                    return SuccessResponse(pagedLinks, _localizer["FormCriterionLinksRetrievedSuccessfully"].Value);
                }
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving criterion links for category: {categoryId}", ex);
                _logger.LogError(ex, "Error retrieving criterion links for category: {CategoryId}", categoryId);
                return HandleException(ex, _localizer["ErrorRetrievingFormCriterionLinks"].Value);
            }
        }
        /// ID'ye göre form kriter bağlantısını getir
        [HttpGet]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> GetFormCriterionLink(string id)
        {
            try
            {
                var link = await _formManager.GetFormCriterionLinkByIdAsync(id);
                if (link == null)
                {
                    return NotFoundResponse(_localizer["FormCriterionLinkNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Retrieved form criterion link: {id}");
                return SuccessResponse(link);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving form criterion link: {id}", ex);
                _logger.LogError(ex, "Error retrieving form criterion link: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Yeni bir form kriter bağlantısı oluştur
        [HttpPost]
        [Authorize(APConsts.Policies.ManageForms)] // Form yönetimi yetkisi
        public async Task<IActionResult> CreateFormCriterionLink([FromBody] FormCriterionLinkCreateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                var createdLink = await _formManager.CreateFormCriterionLinkAsync(dto, userId);
                await _systemLogHelper.LogInfoAsync($"Created form criterion link: {createdLink.Id}");
                return CreatedAtAction(
                    nameof(GetFormCriterionLink),
                    new { id = createdLink.Id },
                    createdLink);
            }
            catch (ArgumentException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Invalid criterion assignment: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error creating form criterion link", ex);
                _logger.LogError(ex, "Error creating form criterion link");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir form kriter bağlantısını güncelle
        [HttpPut]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> UpdateFormCriterionLink(string id, [FromBody] FormCriterionLinkUpdateDto dto)
        {
            try
            {
                if (id != dto.Id)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var success = await _formManager.UpdateFormCriterionLinkAsync(dto);
                if (!success)
                {
                    return NotFoundResponse(_localizer["FormCriterionLinkNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Updated form criterion link: {id}");
                return SuccessResponse(_localizer["FormCriterionLinkUpdatedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating form criterion link: {id}", ex);
                _logger.LogError(ex, "Error updating form criterion link: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir form kriter bağlantısını sil
        [HttpDelete]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> DeleteFormCriterionLink(string id)
        {
            try
            {
                var success = await _formManager.DeleteFormCriterionLinkAsync(id);
                if (!success)
                {
                    return NotFoundResponse(_localizer["FormCriterionLinkNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Deleted form criterion link: {id}");
                return SuccessResponse(_localizer["FormCriterionLinkDeletedSuccessfully"].Value);
            }
            catch (InvalidOperationException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Cannot delete form criterion link {id}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error deleting form criterion link: {id}", ex);
                _logger.LogError(ex, "Error deleting form criterion link: {Id}", id);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        /// Bir form kategorisine birden fazla kriter ata
        [HttpPost]
        [Authorize(APConsts.Policies.ManageForms)] // Form yönetimi yetkisi
        public async Task<IActionResult> AssignCriteriaToFormCategory(string categoryId, [FromBody] CriterionAssignmentDto dto)
        {
            try
            {
                if (categoryId != dto.FormCategoryId)
                {
                    return BadRequestResponse(_localizer["IdMismatch"].Value);
                }
                var userId = _userContextHelper.GetUserId()!;
                var createdLinks = await _formManager.AssignCriteriaToFormCategoryAsync(dto, userId);
                await _systemLogHelper.LogInfoAsync($"Assigned {createdLinks.Count} criteria to category: {categoryId}");
                var result = new
                {
                    message = "Criteria assigned successfully",
                    assignedCriteria = createdLinks.Count,
                    links = createdLinks
                };
                return SuccessResponse(result);
            }
            catch (ArgumentException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Invalid criterion assignment for category {categoryId}: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error assigning criteria to category: {categoryId}", ex);
                _logger.LogError(ex, "Error assigning criteria to category: {CategoryId}", categoryId);
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        #endregion
    }
}
