using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Consts;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Resources;

namespace AcademicPerformance.Controllers
{
    /// <summary>
    /// Controller Dashboard API
    /// </summary>
    [ApiController]
    [Route("[controller]/[action]")]
    public class ControllerDashboardController : BaseApiController
    {
        private readonly IControllerManager _controllerManager;
        private readonly IUserContextHelper _userContextHelper;
        private readonly IRlxSystemLogHelper<ControllerDashboardController> _systemLogHelper;
        private new readonly IStringLocalizer<ControllerDashboardController> _localizer;
        private readonly ILogger<ControllerDashboardController> _logger;

        public ControllerDashboardController(
            IControllerManager controllerManager,
            IUserContextHelper userContextHelper,
            IRlxSystemLogHelper<ControllerDashboardController> systemLogHelper,
            IStringLocalizer<ControllerDashboardController> localizer,
            IStringLocalizer<SharedResource> sharedLocalizer,
            ILogger<ControllerDashboardController> logger) : base(sharedLocalizer)
        {
            _controllerManager = controllerManager;
            _userContextHelper = userContextHelper;
            _systemLogHelper = systemLogHelper;
            _localizer = localizer;
            _logger = logger;
        }

        #region Dashboard Operations

        /// <summary>
        /// Controller dashboard verilerini getir
        /// </summary>
        /// <returns>Controller dashboard verileri</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ApproveSubmissions)]
        public async Task<IActionResult> GetDashboard()
        {
            try
            {
                var controllerId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Controller dashboard istendi: {controllerId}");

                var dashboard = await _controllerManager.GetControllerDashboardAsync(controllerId);

                await _systemLogHelper.LogInfoAsync($"Controller dashboard başarıyla getirildi: {controllerId}");
                return SuccessResponse(dashboard, _localizer["DashboardRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Controller dashboard getirme hatası");
                return HandleException(ex, _localizer["ErrorRetrievingDashboard"].Value);
            }
        }

        /// <summary>
        /// Pending submission'ları sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış pending submissions listesi</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.ApproveSubmissions)]
        public async Task<IActionResult> GetPendingSubmissions([FromBody] PagedListCo<SubmissionFilterCo> co)
        {
            try
            {
                var controllerId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Pending submissions istendi: Controller: {controllerId}, Page: {co.Pager.Page}, Size: {co.Pager.Size}");

                var pendingSubmissions = await _controllerManager.GetPendingSubmissionsAsync(controllerId, co);

                await _systemLogHelper.LogInfoAsync($"Pending submissions başarıyla getirildi: Controller: {controllerId}, Total: {pendingSubmissions.TotalCount}");
                return SuccessResponse(pendingSubmissions, _localizer["PendingSubmissionsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Pending submissions getirme hatası");
                return HandleException(ex, _localizer["ErrorRetrievingPendingSubmissions"].Value);
            }
        }

        /// <summary>
        /// Controller istatistiklerini getir
        /// </summary>
        /// <returns>Controller istatistikleri</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ApproveSubmissions)]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                var controllerId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"Controller statistics istendi: {controllerId}");

                var statistics = await _controllerManager.GetControllerStatisticsAsync(controllerId);

                await _systemLogHelper.LogInfoAsync($"Controller statistics başarıyla getirildi: {controllerId}");
                return SuccessResponse(statistics, _localizer["StatisticsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Controller statistics getirme hatası");
                return HandleException(ex, _localizer["ErrorRetrievingStatistics"].Value);
            }
        }

        #endregion

        #region Test Operations

        /// <summary>
        /// Test endpoint - Controller dashboard functionality test
        /// </summary>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewReports)]
        public async Task<IActionResult> TestDashboard()
        {
            try
            {
                // Test user ID - gerçek implementasyonda token'dan alınacak
                var testControllerId = "test-controller-123";

                _logger.LogInformation($"Test dashboard çağrısı: {testControllerId}");

                var dashboard = await _controllerManager.GetControllerDashboardAsync(testControllerId);

                return SuccessResponse(dashboard, "Test dashboard başarıyla getirildi");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Test dashboard hatası");
                return HandleException(ex, "Test dashboard hatası");
            }
        }

        /// <summary>
        /// Test endpoint - Pending submissions test
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.ViewSubmissions)]
        public async Task<IActionResult> TestPendingSubmissions([FromBody] PagedListCo<SubmissionFilterCo>? co = null)
        {
            try
            {
                // Test user ID
                var testControllerId = "test-controller-123";

                // Default pagination if not provided
                co ??= new PagedListCo<SubmissionFilterCo>
                {
                    Pager = new PagerCo { Page = 1, Size = 10 },
                    Criteria = new SubmissionFilterCo()
                };

                _logger.LogInformation($"Test pending submissions çağrısı: {testControllerId}");

                var pendingSubmissions = await _controllerManager.GetPendingSubmissionsAsync(testControllerId, co);

                return SuccessResponse(pendingSubmissions, "Test pending submissions başarıyla getirildi");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Test pending submissions hatası");
                return HandleException(ex, "Test pending submissions hatası");
            }
        }

        #endregion
    }
}
