using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.Consts;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;

namespace AcademicPerformance.Controllers
{
    /// <summary>
    /// Email notification test ve yönetim controller'ı
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiController]
    public class NotificationController : BaseApiController
    {
        private readonly INotificationService _notificationService;
        private readonly IRlxSystemLogHelper<NotificationController> _systemLogHelper;
        private readonly ILogger<NotificationController> _logger;

        public NotificationController(
            INotificationService notificationService,
            IRlxSystemLogHelper<NotificationController> systemLogHelper,
            ILogger<NotificationController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _notificationService = notificationService;
            _systemLogHelper = systemLogHelper;
            _logger = logger;
        }

        /// <summary>
        /// SMTP bağlantısını test et
        /// </summary>
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> TestSmtpConnection()
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("SMTP bağlantı testi başlatılıyor");
                
                var isConnected = await _notificationService.TestSmtpConnectionAsync();
                
                if (isConnected)
                {
                    return SuccessResponse(new { 
                        Status = "Success", 
                        Message = "SMTP bağlantısı başarılı",
                        Timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    return ErrorResponse(null, "SMTP bağlantısı başarısız");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SMTP bağlantı testi hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Test email gönder
        /// </summary>
        /// <param name="testEmail">Test email adresi</param>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> SendTestEmail([FromQuery] string testEmail)
        {
            try
            {
                if (string.IsNullOrEmpty(testEmail))
                {
                    return BadRequestResponse("Test email adresi gerekli");
                }

                await _systemLogHelper.LogInfoAsync($"Test email gönderiliyor: {testEmail}");
                
                var isSuccess = await _notificationService.SendTestEmailAsync(testEmail);
                
                if (isSuccess)
                {
                    return SuccessResponse(new { 
                        Status = "Success", 
                        Message = $"Test email başarıyla gönderildi: {testEmail}",
                        Timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    return ErrorResponse(null, "Test email gönderilemedi");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Test email gönderim hatası: {TestEmail}", testEmail);
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Submission onaylandı notification test
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> TestSubmissionApprovedNotification([FromQuery] string email, [FromQuery] string submissionId = "TEST-001")
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                {
                    return BadRequestResponse("Email adresi gerekli");
                }

                await _systemLogHelper.LogInfoAsync($"Submission approved notification test: {email}");
                
                var isSuccess = await _notificationService.SendSubmissionApprovedNotificationAsync(
                    submissionId, email, "Test Controller");
                
                if (isSuccess)
                {
                    return SuccessResponse(new { 
                        Status = "Success", 
                        Message = $"Submission approved notification gönderildi: {email}",
                        SubmissionId = submissionId,
                        Timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    return ErrorResponse(null, "Notification gönderilemedi");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Submission approved notification test hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Submission reddedildi notification test
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> TestSubmissionRejectedNotification([FromQuery] string email, [FromQuery] string submissionId = "TEST-001")
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                {
                    return BadRequestResponse("Email adresi gerekli");
                }

                await _systemLogHelper.LogInfoAsync($"Submission rejected notification test: {email}");
                
                var isSuccess = await _notificationService.SendSubmissionRejectedNotificationAsync(
                    submissionId, email, "Test Controller", "Test amaçlı red nedeni");
                
                if (isSuccess)
                {
                    return SuccessResponse(new { 
                        Status = "Success", 
                        Message = $"Submission rejected notification gönderildi: {email}",
                        SubmissionId = submissionId,
                        Timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    return ErrorResponse(null, "Notification gönderilemedi");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Submission rejected notification test hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Yeni submission notification test
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> TestNewSubmissionNotification([FromQuery] string email, [FromQuery] string submissionId = "TEST-001")
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                {
                    return BadRequestResponse("Email adresi gerekli");
                }

                await _systemLogHelper.LogInfoAsync($"New submission notification test: {email}");
                
                var isSuccess = await _notificationService.SendNewSubmissionNotificationAsync(
                    submissionId, email, "Test Akademisyen");
                
                if (isSuccess)
                {
                    return SuccessResponse(new { 
                        Status = "Success", 
                        Message = $"New submission notification gönderildi: {email}",
                        SubmissionId = submissionId,
                        Timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    return ErrorResponse(null, "Notification gönderilemedi");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "New submission notification test hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Portfolio verification notification test
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> TestPortfolioVerificationNotification([FromQuery] string email, [FromQuery] string courseId = "TEST-COURSE-001")
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                {
                    return BadRequestResponse("Email adresi gerekli");
                }

                await _systemLogHelper.LogInfoAsync($"Portfolio verification notification test: {email}");
                
                var isSuccess = await _notificationService.SendPortfolioVerificationNotificationAsync(
                    courseId, email, "Test Akademisyen");
                
                if (isSuccess)
                {
                    return SuccessResponse(new { 
                        Status = "Success", 
                        Message = $"Portfolio verification notification gönderildi: {email}",
                        CourseId = courseId,
                        Timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    return ErrorResponse(null, "Notification gönderilemedi");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Portfolio verification notification test hatası");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Notification sistemi health check
        /// </summary>
        [HttpGet]
        [Authorize(APConsts.Policies.AllAccess)]
        public async Task<IActionResult> HealthCheck()
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("Notification system health check başlatılıyor");
                
                var smtpTest = await _notificationService.TestSmtpConnectionAsync();
                
                var healthStatus = new
                {
                    Status = smtpTest ? "Healthy" : "Unhealthy",
                    Timestamp = DateTime.UtcNow,
                    Components = new
                    {
                        SmtpConnection = smtpTest ? "OK" : "FAILED",
                        TemplateService = "OK", // Template service her zaman çalışır durumda
                        Configuration = "OK"
                    },
                    Details = new
                    {
                        SmtpConnectionTest = smtpTest,
                        AvailableTemplates = new[]
                        {
                            "SubmissionApproved",
                            "SubmissionRejected", 
                            "NewSubmission",
                            "RevisionRequest",
                            "DeadlineReminder",
                            "PortfolioVerification",
                            "StaffCompetencyEvaluation",
                            "DepartmentPerformance",
                            "TestEmail"
                        }
                    }
                };
                
                return SuccessResponse(healthStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Notification health check hatası");
                return HandleException(ex);
            }
        }
    }
}
