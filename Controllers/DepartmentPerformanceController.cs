using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Consts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Resources;

namespace AcademicPerformance.Controllers
{
    /// <summary>
    /// Bölüm performans yönetimi endpoint'leri
    /// </summary>
    [ApiController]
    [Route("[controller]/[action]")]
    [Authorize(APConsts.Policies.RequireAdminRole)]
    public class DepartmentPerformanceController : BaseApiController
    {
        private readonly IDepartmentPerformanceManager _departmentPerformanceManager;
        private readonly ILogger<DepartmentPerformanceController> _logger;

        public DepartmentPerformanceController(
            IDepartmentPerformanceManager departmentPerformanceManager,
            ILogger<DepartmentPerformanceController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _departmentPerformanceManager = departmentPerformanceManager ?? throw new ArgumentNullException(nameof(departmentPerformanceManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region CRUD Operations

        /// <summary>
        /// Bölüm performans kaydı oluştur
        /// </summary>
        /// <param name="dto">Oluşturma DTO'su</param>
        /// <returns>Oluşturulan bölüm performans DTO'su</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.InputDepartmentData)]
        public async Task<IActionResult> CreateDepartmentPerformance([FromBody] DepartmentPerformanceCreateDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var userId = User.Identity?.Name;
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse();
                }

                var result = await _departmentPerformanceManager.CreateDepartmentPerformanceAsync(dto, userId);
                return SuccessResponse(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Bölüm performans kaydı oluşturulurken doğrulama hatası");
                return BadRequestResponse(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Bölüm performans kaydı oluşturulurken işlem hatası");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans kaydı oluşturulurken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm performans kaydını güncelle
        /// </summary>
        /// <param name="dto">Güncelleme DTO'su</param>
        /// <returns>İşlem sonucu</returns>
        [HttpPut]
        [Authorize(APConsts.Policies.InputDepartmentData)]
        public async Task<IActionResult> UpdateDepartmentPerformance([FromBody] DepartmentPerformanceUpdateDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var userId = User.Identity?.Name;
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse();
                }

                var result = await _departmentPerformanceManager.UpdateDepartmentPerformanceAsync(dto, userId);
                if (!result)
                {
                    return NotFoundResponse(_localizer["DepartmentPerformanceNotFound"].Value);
                }

                return SuccessResponse(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Bölüm performans kaydı güncellenirken doğrulama hatası");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans kaydı güncellenirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm performans kaydını sil
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <returns>İşlem sonucu</returns>
        [HttpDelete("{id}")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> DeleteDepartmentPerformance(string id)
        {
            try
            {
                var userId = User.Identity?.Name;
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse();
                }

                var result = await _departmentPerformanceManager.DeleteDepartmentPerformanceAsync(id, userId);
                if (!result)
                {
                    return NotFoundResponse(_localizer["DepartmentPerformanceNotFound"].Value);
                }

                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans kaydı silinirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm performans kaydını getir
        /// </summary>
        /// <param name="id">Kayıt ID'si</param>
        /// <returns>Bölüm performans DTO'su</returns>
        [HttpGet("{id}")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GetDepartmentPerformance(string id)
        {
            try
            {
                var result = await _departmentPerformanceManager.GetDepartmentPerformanceAsync(id);
                if (result == null)
                {
                    return NotFoundResponse(_localizer["DepartmentPerformanceNotFound"].Value);
                }

                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans kaydı getirilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm performans kayıtlarını filtreli listele
        /// </summary>
        /// <param name="co">Filtreleme ve sayfalama parametreleri</param>
        /// <returns>Sayfalanmış bölüm performans listesi</returns>
        [HttpPost("List")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GetDepartmentPerformances([FromBody] PagedListCo<DepartmentPerformanceFilterCo> co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.GetDepartmentPerformancesAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans kayıtları listelenirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        #endregion

        #region Dashboard Operations

        /// <summary>
        /// Bölüm dashboard'ını getir
        /// </summary>
        /// <param name="co">Dashboard parametreleri</param>
        /// <returns>Bölüm dashboard DTO'su</returns>
        [HttpPost("Dashboard")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GetDepartmentDashboard([FromBody] DepartmentDashboardCo co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.GetDepartmentDashboardAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm dashboard'ı getirilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm özet bilgilerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem (opsiyonel)</param>
        /// <returns>Bölüm performans özet DTO'su</returns>
        [HttpGet("Summary/{departmentId}")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GetDepartmentSummary(string departmentId, [FromQuery] string? period = null)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId))
                {
                    return BadRequestResponse(_localizer["DepartmentIdRequired"].Value);
                }

                var result = await _departmentPerformanceManager.GetDepartmentSummaryAsync(departmentId, period);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm özet bilgileri getirilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm istatistiklerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="periodCount">Dönem sayısı</param>
        /// <returns>Bölüm istatistikleri DTO'su</returns>
        [HttpGet("Statistics/{departmentId}")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GetDepartmentStatistics(string departmentId, [FromQuery] int periodCount = 12)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId))
                {
                    return BadRequestResponse(_localizer["DepartmentIdRequired"].Value);
                }

                var result = await _departmentPerformanceManager.GetDepartmentStatisticsAsync(departmentId, periodCount);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm istatistikleri getirilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        #endregion

        #region Comparison & Analysis

        /// <summary>
        /// Bölüm karşılaştırması yap
        /// </summary>
        /// <param name="co">Karşılaştırma parametreleri</param>
        /// <returns>Bölüm karşılaştırma DTO'su</returns>
        [HttpPost("Compare")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> CompareDepartments([FromBody] DepartmentComparisonCo co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.CompareDepartmentsAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm karşılaştırması yapılırken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm trend analizi yap
        /// </summary>
        /// <param name="co">Trend analizi parametreleri</param>
        /// <returns>Bölüm trend analizi DTO'su</returns>
        [HttpPost("TrendAnalysis")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> AnalyzeDepartmentTrend([FromBody] DepartmentTrendAnalysisCo co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.AnalyzeDepartmentTrendAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm trend analizi yapılırken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm benchmark'ını hesapla
        /// </summary>
        /// <param name="co">Benchmark parametreleri</param>
        /// <returns>Bölüm benchmark DTO'su</returns>
        [HttpPost("Benchmark")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> CalculateDepartmentBenchmark([FromBody] DepartmentBenchmarkCo co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.CalculateDepartmentBenchmarkAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm benchmark'ı hesaplanırken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        #endregion

        #region Ranking Operations

        /// <summary>
        /// Bölüm sıralamasını getir
        /// </summary>
        /// <param name="co">Sıralama parametreleri</param>
        /// <returns>Sıralanmış bölüm listesi</returns>
        [HttpPost("Ranking")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GetDepartmentRanking([FromBody] DepartmentRankingCo co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.GetDepartmentRankingAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm sıralaması getirilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Belirli bir bölümün sıralama bilgilerini getir
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Bölüm sıralama DTO'su</returns>
        [HttpGet("RankingInfo/{departmentId}")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GetDepartmentRankingInfo(string departmentId, [FromQuery] string period)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId) || string.IsNullOrEmpty(period))
                {
                    return BadRequestResponse(_localizer["RequiredParametersMissing"].Value);
                }

                var result = await _departmentPerformanceManager.GetDepartmentRankingInfoAsync(departmentId, period);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm sıralama bilgileri getirilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        #endregion

        #region Report Operations

        /// <summary>
        /// Bölüm performans raporu oluştur
        /// </summary>
        /// <param name="co">Rapor parametreleri</param>
        /// <returns>Rapor verisi</returns>
        [HttpPost("GenerateReport")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> GenerateDepartmentReport([FromBody] DepartmentPerformanceReportCo co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.GenerateDepartmentReportAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans raporu oluşturulurken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm performans raporunu export et
        /// </summary>
        /// <param name="co">Rapor parametreleri</param>
        /// <returns>Export edilen dosya bilgileri</returns>
        [HttpPost("ExportReport")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> ExportDepartmentReport([FromBody] DepartmentPerformanceReportCo co)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequestResponse(_localizer["InvalidModelState"].Value);
                }

                var result = await _departmentPerformanceManager.ExportDepartmentReportAsync(co);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans raporu export edilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        #endregion

        #region Calculation Operations

        /// <summary>
        /// Bölüm genel skorunu hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Hesaplanan genel skor</returns>
        [HttpGet("CalculateOverallScore/{departmentId}")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> CalculateDepartmentOverallScore(string departmentId, [FromQuery] string period)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId) || string.IsNullOrEmpty(period))
                {
                    return BadRequestResponse(_localizer["RequiredParametersMissing"].Value);
                }

                var result = await _departmentPerformanceManager.CalculateDepartmentOverallScoreAsync(departmentId, period);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm genel skoru hesaplanırken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm tamamlanma oranını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Tamamlanma oranı (0-100)</returns>
        [HttpGet("CalculateCompletionRate/{departmentId}")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> CalculateDepartmentCompletionRate(string departmentId, [FromQuery] string period)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId) || string.IsNullOrEmpty(period))
                {
                    return BadRequestResponse(_localizer["RequiredParametersMissing"].Value);
                }

                var result = await _departmentPerformanceManager.CalculateDepartmentCompletionRateAsync(departmentId, period);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm tamamlanma oranı hesaplanırken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm büyüme oranını hesapla
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="currentPeriod">Mevcut dönem</param>
        /// <param name="previousPeriod">Önceki dönem</param>
        /// <returns>Büyüme oranı (%)</returns>
        [HttpGet("CalculateGrowthRate/{departmentId}")]
        [Authorize(APConsts.Policies.AccessReporting)]
        public async Task<IActionResult> CalculateDepartmentGrowthRate(
            string departmentId,
            [FromQuery] string currentPeriod,
            [FromQuery] string previousPeriod)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId) || string.IsNullOrEmpty(currentPeriod) || string.IsNullOrEmpty(previousPeriod))
                {
                    return BadRequestResponse(_localizer["RequiredParametersMissing"].Value);
                }

                var result = await _departmentPerformanceManager.CalculateDepartmentGrowthRateAsync(departmentId, currentPeriod, previousPeriod);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm büyüme oranı hesaplanırken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        #endregion

        #region Utility Operations

        /// <summary>
        /// Bölüm performans verilerini senkronize et
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <param name="period">Dönem</param>
        /// <returns>Senkronizasyon başarılı mı</returns>
        [HttpPost("Synchronize/{departmentId}")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> SynchronizeDepartmentData(string departmentId, [FromQuery] string period)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId) || string.IsNullOrEmpty(period))
                {
                    return BadRequestResponse(_localizer["RequiredParametersMissing"].Value);
                }

                var result = await _departmentPerformanceManager.SynchronizeDepartmentDataAsync(departmentId, period);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm verileri senkronize edilirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bölüm performans cache'ini temizle
        /// </summary>
        /// <param name="departmentId">Bölüm ID'si</param>
        /// <returns>Temizleme başarılı mı</returns>
        [HttpPost("ClearCache/{departmentId}")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> ClearDepartmentPerformanceCache(string departmentId)
        {
            try
            {
                if (string.IsNullOrEmpty(departmentId))
                {
                    return BadRequestResponse(_localizer["DepartmentIdRequired"].Value);
                }

                var result = await _departmentPerformanceManager.ClearDepartmentPerformanceCacheAsync(departmentId);
                return SuccessResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans cache'i temizlenirken beklenmeyen hata");
                return HandleException(ex);
            }
        }

        #endregion
    }
}
