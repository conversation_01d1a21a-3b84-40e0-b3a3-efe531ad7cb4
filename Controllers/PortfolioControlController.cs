using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Consts;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
using System.ComponentModel.DataAnnotations;
namespace AcademicPerformance.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class PortfolioControlController : BaseApiController
    {
        private readonly IPortfolioControlManager _portfolioControlManager;
        private readonly IUserContextHelper _userContextHelper;
        private readonly ILogger<PortfolioControlController> _logger;
        public PortfolioControlController(
            IPortfolioControlManager portfolioControlManager,
            IUserContextHelper userContextHelper,
            ILogger<PortfolioControlController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _portfolioControlManager = portfolioControlManager;
            _userContextHelper = userContextHelper;
            _logger = logger;
        }
        #region Course Portfolio Verification Operations
        /// <summary>
        /// Verification bekleyen ders bilgilerini getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC (opsiyonel)</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Bekleyen verification listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.VerifyPortfolio)]

        public async Task<IActionResult> GetPendingCourseVerifications(
            [FromQuery] string? academicianTc = null,
            [FromQuery] string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting pending course verifications for academician: {AcademicianTc}, period: {Period}",
                    academicianTc, period);
                var archivistId = _userContextHelper.GetUserId()!;
                var pendingVerifications = await _portfolioControlManager.GetPendingCourseVerificationsAsync(archivistId, period);
                if (!string.IsNullOrEmpty(academicianTc))
                {
                    pendingVerifications = pendingVerifications.Where(v => v.AcademicianTc == academicianTc).ToList();
                }
                var verificationCount = pendingVerifications.Count();
                _logger.LogInformation("Successfully retrieved {Count} pending verifications", verificationCount);
                return SuccessResponse(pendingVerifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending course verifications");
                return HandleException(ex, "Bekleyen verification'lar getirilirken bir hata oluştu");
            }
        }
        /// <summary>
        /// Akademisyenin ders verification durumunu getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <returns>Akademisyenin tüm ders verification'ları</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> GetAcademicianCourseVerifications(
            [Required][StringLength(11, MinimumLength = 11)] string academicianTc)
        {
            try
            {
                _logger.LogInformation("Getting course verifications for academician: {AcademicianTc}", academicianTc);
                var verifications = await _portfolioControlManager.GetAcademicianCourseVerificationsAsync(academicianTc);
                if (!verifications.Any())
                {
                    _logger.LogWarning("No verifications found for academician: {AcademicianTc}", academicianTc);
                    return NotFoundResponse("Bu akademisyen için verification kaydı bulunamadı");
                }
                _logger.LogInformation("Successfully retrieved {Count} verifications for academician: {AcademicianTc}",
                    verifications.Count(), academicianTc);
                return SuccessResponse(verifications);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid academician TC: {AcademicianTc}", academicianTc);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting course verifications for academician: {AcademicianTc}", academicianTc);
                return HandleException(ex, "Akademisyen verification'ları getirilirken bir hata oluştu");
            }
        }
        /// <summary>
        /// Ders verification status günceller
        /// </summary>
        /// <param name="verificationId">Verification ID</param>
        /// <param name="dto">Güncelleme verileri</param>
        /// <returns>Güncellenmiş verification</returns>
        [HttpPut]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> UpdateCourseVerification(
            [Required] string verificationId,
            [FromBody] CourseVerificationUpdateDto dto)
        {
            try
            {
                _logger.LogInformation("Updating course verification: {VerificationId}", verificationId);
                var archivistId = _userContextHelper.GetUserId()!;
                dto.Id = verificationId; // Set the ID in the DTO
                var updatedVerification = await _portfolioControlManager.UpdateCourseVerificationAsync(dto, archivistId);
                if (updatedVerification == null)
                {
                    _logger.LogWarning("Verification not found: {VerificationId}", verificationId);
                    return NotFoundResponse("Verification kaydı bulunamadı");
                }
                _logger.LogInformation("Successfully updated verification: {VerificationId}", verificationId);
                return SuccessResponse(updatedVerification);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid update data for verification: {VerificationId}", verificationId);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating verification: {VerificationId}", verificationId);
                return HandleException(ex, "Verification güncellenirken bir hata oluştu");
            }
        }
        /// <summary>
        /// Toplu ders verification güncelleme
        /// </summary>
        /// <param name="dto">Toplu güncelleme verileri</param>
        /// <returns>Güncelleme sonucu</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> BulkUpdateCourseVerifications(
            [FromBody] BulkCourseVerificationUpdateDto dto)
        {
            try
            {
                _logger.LogInformation("Starting bulk update for {Count} verifications", dto.VerificationIds.Count);
                var archivistId = _userContextHelper.GetUserId()!;
                var result = await _portfolioControlManager.BulkUpdateCourseVerificationsAsync(dto, archivistId);
                _logger.LogInformation("Bulk update completed. Success: {Success}, Failed: {Failed}",
                    result.SuccessCount, result.FailedCount);
                return SuccessResponse(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid bulk update data");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk update");
                return HandleException(ex, "Toplu güncelleme sırasında bir hata oluştu");
            }
        }
        #endregion
        #region ArelBridge Integration
        /// <summary>
        /// ArelBridge'den ders bilgilerini senkronize eder
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <returns>Senkronizasyon sonucu</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> SyncCoursesFromArelBridge(
            [Required][StringLength(11, MinimumLength = 11)] string academicianTc)
        {
            try
            {
                _logger.LogInformation("Starting course sync from ArelBridge for academician: {AcademicianTc}", academicianTc);
                var result = await _portfolioControlManager.SyncAcademicianCourseVerificationsAsync(academicianTc);
                if (result.IsSuccessful)
                {
                    _logger.LogInformation("Course sync completed successfully. Created: {Created}, Updated: {Updated}, Skipped: {Skipped}",
                        result.CreatedCount, result.UpdatedCount, result.SkippedCount);
                }
                else
                {
                    _logger.LogWarning("Course sync failed with errors: {Errors}", string.Join(", ", result.ErrorMessages));
                }
                return SuccessResponse(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid academician TC for sync: {AcademicianTc}", academicianTc);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during course sync for academician: {AcademicianTc}", academicianTc);
                return HandleException(ex, "Ders senkronizasyonu sırasında bir hata oluştu");
            }
        }
        #endregion
        #region Dashboard Operations
        /// <summary>
        /// Archivist dashboard verilerini getirir
        /// </summary>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Dashboard verileri</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> GetArchivistDashboard([FromQuery] string? period = null)
        {
            try
            {
                _logger.LogInformation("Getting archivist dashboard for period: {Period}", period);
                var archivistId = _userContextHelper.GetUserId()!;
                var dashboard = await _portfolioControlManager.GetArchivistDashboardAsync(archivistId, period);
                _logger.LogInformation("Successfully retrieved dashboard data with {PendingCount} pending verifications",
                    dashboard.PendingVerifications.Count);
                return SuccessResponse(dashboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archivist dashboard");
                return HandleException(ex, "Dashboard verileri getirilirken bir hata oluştu");
            }
        }
        /// <summary>
        /// Ders verification'larını arama ve filtreleme
        /// </summary>
        /// <param name="request">Arama ve filtreleme parametreleri</param>
        /// <returns>Filtrelenmiş verification listesi</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> SearchCourseVerifications([FromBody] SearchCourseVerificationRequestDto request)
        {
            try
            {
                _logger.LogInformation("Searching course verifications with filters: {@Request}", request);
                var archivistId = _userContextHelper.GetUserId()!;
                request.ArchivistId = archivistId; // Ensure archivist can only see their own data
                var result = await _portfolioControlManager.SearchCourseVerificationsAsync(request);
                _logger.LogInformation("Search completed. Found {Count} verifications out of {Total} total",
                    result.Data?.Count() ?? 0, result.TotalCount);
                return SuccessResponse(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid search request: {@Request}", request);
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching course verifications");
                return HandleException(ex, "Verification arama işlemi sırasında bir hata oluştu");
            }
        }
        /// <summary>
        /// Portfolio verification istatistiklerini getirir
        /// </summary>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <param name="academicianTc">Akademisyen TC (opsiyonel)</param>
        /// <returns>İstatistik verileri</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> GetVerificationStatistics(
            [FromQuery] string? period = null,
            [FromQuery] string? academicianTc = null)
        {
            try
            {
                _logger.LogInformation("Getting verification statistics for period: {Period}, academician: {AcademicianTc}",
                    period, academicianTc);
                var statistics = await _portfolioControlManager.GetDashboardStatisticsAsync(period);
                _logger.LogInformation("Successfully retrieved verification statistics");
                return SuccessResponse(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verification statistics");
                return HandleException(ex, "İstatistikler getirilirken bir hata oluştu");
            }
        }
        #endregion
        #region Health Check
        /// <summary>
        /// Portfolio Control modülü sağlık kontrolü
        /// </summary>
        /// <returns>Sağlık durumu</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.VerifyPortfolio)]
        public async Task<IActionResult> HealthCheck()
        {
            try
            {
                var (isHealthy, message, details) = await _portfolioControlManager.HealthCheckAsync();
                if (isHealthy)
                {
                    return Ok(new { Status = "Healthy", Message = message, Details = details });
                }
                else
                {
                    return StatusCode(StatusCodes.Status503ServiceUnavailable,
                        new { Status = "Unhealthy", Message = message, Details = details });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new { Status = "Unhealthy", Message = "Health check failed", Details = ex.Message });
            }
        }
        #endregion
    }
}
