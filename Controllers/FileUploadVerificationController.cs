using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Services.Interfaces;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Consts;
using AcademicPerformance.Controllers.Base;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;

namespace AcademicPerformance.Controllers;

/// <summary>
/// File upload verification and testing controller
/// </summary>
[ApiController]
[Route("[controller]/[action]")]
public class FileUploadVerificationController : BaseApiController
{
    private readonly IMinIOFileService _minioFileService;
    private readonly AcademicPerformanceDbContext _dbContext;
    private readonly ILogger<FileUploadVerificationController> _logger;
    private readonly IRlxSystemLogHelper<FileUploadVerificationController> _systemLogHelper;

    public FileUploadVerificationController(
        IMinIOFileService minioFileService,
        AcademicPerformanceDbContext dbContext,
        ILogger<FileUploadVerificationController> logger,
        IRlxSystemLogHelper<FileUploadVerificationController> systemLogHelper,
        IStringLocalizer<SharedResource> localizer) : base(localizer)
    {
        _minioFileService = minioFileService;
        _dbContext = dbContext;
        _logger = logger;
        _systemLogHelper = systemLogHelper;
    }

    /// <summary>
    /// Verify all file upload endpoints are working
    /// </summary>
    [HttpGet]
    [Authorize(APConsts.Policies.ManageFiles)]
    public async Task<IActionResult> VerifyAllEndpoints()
    {
        try
        {
            _logger.LogInformation("File upload endpoints verification başlatılıyor...");

            var results = new List<object>();

            // Test 1: MinIO Service Health Check
            try
            {
                var bucketExists = await _minioFileService.BucketExistsAsync("apdys-evidence-files");
                results.Add(new
                {
                    endpoint = "MinIO Service",
                    status = bucketExists ? "Success" : "Warning",
                    message = bucketExists ? "MinIO service erişilebilir" : "MinIO bucket bulunamadı",
                    details = new { bucketExists = bucketExists }
                });
            }
            catch (Exception ex)
            {
                results.Add(new
                {
                    endpoint = "MinIO Service",
                    status = "Error",
                    message = "MinIO service erişim hatası",
                    error = ex.Message
                });
            }

            // Test 2: Database Connection Check
            try
            {
                var canConnect = await _dbContext.Database.CanConnectAsync();
                var evidenceFileCount = await _dbContext.EvidenceFiles.CountAsync();
                results.Add(new
                {
                    endpoint = "Database Connection",
                    status = canConnect ? "Success" : "Error",
                    message = canConnect ? "Database bağlantısı başarılı" : "Database bağlantı hatası",
                    details = new { canConnect = canConnect, evidenceFileCount = evidenceFileCount }
                });
            }
            catch (Exception ex)
            {
                results.Add(new
                {
                    endpoint = "Database Connection",
                    status = "Error",
                    message = "Database bağlantı hatası",
                    error = ex.Message
                });
            }

            // Test 3: Academic Submissions Check
            try
            {
                var submissionCount = await _dbContext.AcademicSubmissions.CountAsync();
                var hasTestSubmission = await _dbContext.AcademicSubmissions.AnyAsync(s => s.AutoIncrementId == 1);
                results.Add(new
                {
                    endpoint = "Academic Submissions",
                    status = submissionCount > 0 ? "Success" : "Warning",
                    message = $"{submissionCount} academic submission bulundu",
                    details = new { submissionCount = submissionCount, hasTestSubmission = hasTestSubmission }
                });
            }
            catch (Exception ex)
            {
                results.Add(new
                {
                    endpoint = "Academic Submissions",
                    status = "Error",
                    message = "Academic submissions kontrol hatası",
                    error = ex.Message
                });
            }

            // Test 4: Evidence Files Check
            try
            {
                var evidenceFileCount = await _dbContext.EvidenceFiles.CountAsync();
                var recentFiles = await _dbContext.EvidenceFiles
                    .Where(f => f.UploadedAt >= DateTime.UtcNow.AddDays(-7))
                    .CountAsync();
                results.Add(new
                {
                    endpoint = "Evidence Files",
                    status = "Success",
                    message = $"{evidenceFileCount} evidence file bulundu",
                    details = new { totalFiles = evidenceFileCount, recentFiles = recentFiles }
                });
            }
            catch (Exception ex)
            {
                results.Add(new
                {
                    endpoint = "Evidence Files",
                    status = "Error",
                    message = "Evidence files kontrol hatası",
                    error = ex.Message
                });
            }

            var allSuccessful = results.All(r => r.GetType().GetProperty("status")?.GetValue(r)?.ToString() == "Success");
            var hasErrors = results.Any(r => r.GetType().GetProperty("status")?.GetValue(r)?.ToString() == "Error");

            _logger.LogInformation("File upload endpoints verification tamamlandı: {AllSuccessful}", allSuccessful);

            return Ok(new
            {
                status = hasErrors ? "Error" : (allSuccessful ? "Success" : "Warning"),
                message = "File upload endpoints verification tamamlandı",
                timestamp = DateTime.UtcNow,
                allSuccessful = allSuccessful,
                hasErrors = hasErrors,
                results = results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "File upload endpoints verification genel hatası");
            return StatusCode(500, new { status = "Error", message = "Verification sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Get file upload system statistics
    /// </summary>
    [HttpGet]
    [Authorize(APConsts.Policies.ViewReports)]
    public async Task<IActionResult> GetSystemStatistics()
    {
        try
        {
            _logger.LogInformation("File upload system statistics alınıyor...");

            // Database statistics
            var totalEvidenceFiles = await _dbContext.EvidenceFiles.CountAsync();
            var totalSizeBytes = await _dbContext.EvidenceFiles.SumAsync(f => f.SizeBytes);
            var filesLast24h = await _dbContext.EvidenceFiles
                .Where(f => f.UploadedAt >= DateTime.UtcNow.AddDays(-1))
                .CountAsync();
            var filesLast7d = await _dbContext.EvidenceFiles
                .Where(f => f.UploadedAt >= DateTime.UtcNow.AddDays(-7))
                .CountAsync();

            // File type statistics
            var fileTypeStats = await _dbContext.EvidenceFiles
                .GroupBy(f => f.ContentType)
                .Select(g => new { ContentType = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(10)
                .ToListAsync();

            // Storage type statistics
            var storageTypeStats = await _dbContext.EvidenceFiles
                .GroupBy(f => f.StorageType)
                .Select(g => new { StorageType = g.Key, Count = g.Count() })
                .ToListAsync();

            // Academic submission statistics
            var submissionsWithFiles = await _dbContext.EvidenceFiles
                .Select(f => f.AcademicSubmissionAutoIncrementId)
                .Distinct()
                .CountAsync();

            var totalSubmissions = await _dbContext.AcademicSubmissions.CountAsync();

            return Ok(new
            {
                status = "Success",
                message = "System statistics alındı",
                timestamp = DateTime.UtcNow,
                statistics = new
                {
                    files = new
                    {
                        total = totalEvidenceFiles,
                        totalSizeMB = Math.Round(totalSizeBytes / (1024.0 * 1024.0), 2),
                        last24Hours = filesLast24h,
                        last7Days = filesLast7d
                    },
                    fileTypes = fileTypeStats,
                    storageTypes = storageTypeStats,
                    submissions = new
                    {
                        total = totalSubmissions,
                        withFiles = submissionsWithFiles,
                        withoutFiles = totalSubmissions - submissionsWithFiles
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "System statistics alma hatası");
            return StatusCode(500, new { status = "Error", message = "Statistics alma sırasında hata oluştu", error = ex.Message });
        }
    }

    /// <summary>
    /// Test file upload workflow end-to-end
    /// </summary>
    [HttpPost]
    [Authorize(APConsts.Policies.ManageFiles)]
    public async Task<IActionResult> TestUploadWorkflow()
    {
        try
        {
            _logger.LogInformation("File upload workflow test başlatılıyor...");

            var testResults = new List<object>();

            // Test 1: File validation test
            var testContent = "Test file content for workflow validation";
            var testBytes = System.Text.Encoding.UTF8.GetBytes(testContent);
            using var testStream = new MemoryStream(testBytes);

            var validationResult = await _minioFileService.ValidateFileAsync(testStream, "test.txt", "text/plain");
            testResults.Add(new
            {
                step = "File Validation",
                status = validationResult.IsValid ? "Success" : "Error",
                message = validationResult.IsValid ? "File validation başarılı" : "File validation başarısız",
                details = new { isValid = validationResult.IsValid, errors = validationResult.Errors }
            });

            // Test 2: Unique object name generation
            var objectName = _minioFileService.GenerateUniqueObjectName("test.txt", "test");
            testResults.Add(new
            {
                step = "Object Name Generation",
                status = !string.IsNullOrEmpty(objectName) ? "Success" : "Error",
                message = "Unique object name oluşturuldu",
                details = new { objectName = objectName }
            });

            // Test 3: MinIO bucket check
            var bucketExists = await _minioFileService.BucketExistsAsync("apdys-evidence-files");
            testResults.Add(new
            {
                step = "Bucket Existence Check",
                status = bucketExists ? "Success" : "Warning",
                message = bucketExists ? "Bucket mevcut" : "Bucket bulunamadı",
                details = new { bucketExists = bucketExists }
            });

            var allStepsSuccessful = testResults.All(r => r.GetType().GetProperty("status")?.GetValue(r)?.ToString() == "Success");

            return Ok(new
            {
                status = allStepsSuccessful ? "Success" : "PartialSuccess",
                message = "Upload workflow test tamamlandı",
                timestamp = DateTime.UtcNow,
                allStepsSuccessful = allStepsSuccessful,
                testResults = testResults
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Upload workflow test hatası");
            return StatusCode(500, new { status = "Error", message = "Workflow test sırasında hata oluştu", error = ex.Message });
        }
    }
}
